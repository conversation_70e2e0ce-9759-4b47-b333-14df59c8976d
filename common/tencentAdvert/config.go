package tencentAdvert

import (
	"github.com/tencentad/marketing-api-go-sdk/pkg/model"
	"github.com/zeromicro/go-zero/core/service"
)

type Config struct {
	ClientId                       int64
	AccountId                      int64
	AccessToken                    string
	ReportV1IosAppId               int64
	ReportV1AndroidAppId           int64
	ReportV1CommodityWarehouse     string
	ReportV1CommodityWarehouseType model.ActionProductInformType
	ReportV1WeMiniAppId            string
}

// DefaultConfig 临时使用 可通过nacos配置文件获取
func DefaultConfig(mode string) *Config {
	if mode == service.ProMode {
		return &Config{
			ClientId:                       **********,
			AccountId:                      ********,
			AccessToken:                    "63fef61d380ee97deee64869d83395ad",
			ReportV1IosAppId:               int64(*********),
			ReportV1AndroidAppId:           int64(**********),
			ReportV1CommodityWarehouse:     "590127",
			ReportV1CommodityWarehouseType: "EC",
		}
	} else {
		return &Config{
			ClientId:                       **********,
			AccountId:                      1183,
			AccessToken:                    "3504ab2b8bc5e31f405b5de723f7417f",
			ReportV1IosAppId:               int64(*********),
			ReportV1AndroidAppId:           int64(**********),
			ReportV1CommodityWarehouse:     "590127",
			ReportV1CommodityWarehouseType: "EC",
		}
	}
}
