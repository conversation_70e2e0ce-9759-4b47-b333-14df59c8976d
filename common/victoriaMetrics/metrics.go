package victoriaMe

import (
	"encoding/json"
	"fmt"
	"github.com/gomodule/redigo/redis"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
)

// MetricsClient 配置
type MetricsClient struct {
	client   *resty.Client
	cache    *RedisCache
	baseURL  string
	username string
	password string
}

// NewMetricsClient 创建新的 Metrics 客户端
func NewMetricsClient(redisPool *redis.Pool, baseURL, username, password string) *MetricsClient {
	return &MetricsClient{
		client: resty.New().
			SetBasicAuth(username, password).
			SetTimeout(5 * time.Second).
			SetDisableWarn(true),
		cache:    &RedisCache{Pool: redisPool},
		baseURL:  baseURL,
		username: username,
		password: password,
	}
}

// Metric 结构
type Metric struct {
	Metric     map[string]string `json:"metric"`
	Values     []float64         `json:"values"`
	Timestamps []int64           `json:"timestamps"`
}

// Report 上报函数
func (m *MetricsClient) Report(metrics []Metric) error {
	var sb strings.Builder

	// 将每个 Metric 转换为 JSON 并拼接到 strings.Builder 中
	for _, metric := range metrics {
		js, err := json.Marshal(metric)
		if err != nil {
			return fmt.Errorf("failed to marshal metric: %v", err)
		}
		// 每个 JSON 对象后加换行符，形成 jsonl 格式
		sb.Write(js)
		sb.WriteString("\n")
	}

	// 一次性发送所有数据
	resp, err := m.client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(sb.String()). // 使用拼接的 JSONL 格式字符串
		Post(m.baseURL + "/api/v1/import")

	if err != nil {
		return fmt.Errorf("HTTP error: %v", err)
	}

	if resp.StatusCode() != 204 {
		return fmt.Errorf("unexpected response code: %d", resp.StatusCode())
	}

	// 打印调试信息
	//fmt.Println(sb.String())
	//fmt.Println(resp.StatusCode())

	return nil
}

// PeriodReportBatchMetrics  批量上报数据
func (m *MetricsClient) PeriodReportBatchMetrics(metricsList []struct {
	ID         int64
	UID        string
	Device     string
	MetricName string
	TimeStamp  int64
}) error {
	var payload []Metric

	for _, data := range metricsList {
		// 检查去重
		if m.cache.IsDuplicate(fmt.Sprintf("%d:%s", data.ID, data.UID), data.MetricName) {
			continue
		}
		payload = append(payload, Metric{
			Metric: map[string]string{
				"__name__": data.MetricName,
				"id":       fmt.Sprintf("%d", data.ID),
				"uid":      data.UID,
				"device":   data.Device,
			},
			Values:     []float64{1},
			Timestamps: []int64{data.TimeStamp},
		})
	}

	if len(payload) == 0 {
		return nil // 没有需要上报的数据
	}

	return m.Report(payload)
}

type QueryResponse struct {
	Data struct {
		Result []struct {
			Metric map[string]string `json:"metric"`
			Value  []interface{}     `json:"value"`
		} `json:"result"`
	} `json:"data"`
}

func (m *MetricsClient) Query(query string) (*QueryResponse, error) {
	var result QueryResponse

	resp, err := m.client.R().
		SetQueryParam("query", query).
		SetResult(&result).
		Get(m.baseURL + "/api/v1/query")

	if err != nil {
		return nil, fmt.Errorf("HTTP error: %v", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("MetricsClient query response code: %d", resp.StatusCode())
	}

	return &result, nil
}

type QueryRangeResponse struct {
	Status string `json:"status"`
	Data   struct {
		ResultType string `json:"resultType"`
		Result     []struct {
			Metric map[string]string `json:"metric"`
			Values [][]interface{}   `json:"values"` // [ [timestamp, value], ... ]
		} `json:"result"`
	} `json:"data"`
}

// QueryRange 适用于时间序列查询（区间）
func (m *MetricsClient) QueryRange(query string, start, end time.Time, step time.Duration) (*QueryRangeResponse, error) {
	var result QueryRangeResponse

	resp, err := m.client.R().
		SetQueryParams(map[string]string{
			"query": query,
			"start": fmt.Sprintf("%d", start.Unix()),
			"end":   fmt.Sprintf("%d", end.Unix()),
			"step":  fmt.Sprintf("%.0f", step.Seconds()), // VictoriaMetrics 要求以秒为单位
		}).
		SetResult(&result).
		Get(m.baseURL + "/api/v1/query_range")

	if err != nil {
		return nil, fmt.Errorf("HTTP error: %v", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("MetricsClient query_range response code: %d", resp.StatusCode())
	}

	return &result, nil
}
