package victoriaMe

import (
	"fmt"
	"time"

	"github.com/gomodule/redigo/redis"
)

// CacheConfig 用于存储不同指标的缓存配置
type CacheConfig struct {
	EnableDedup bool          // 是否去重
	ExpireTime  time.Duration // 过期时间
}

// 默认缓存配置
var defaultCacheConfig = map[string]CacheConfig{
	PeriodExposure: {EnableDedup: true, ExpireTime: 10 * time.Minute},
}

// RedisCache 结构体
type RedisCache struct {
	Pool *redis.Pool
}

// IsDuplicate 是否重复（基于 Redis 去重）
func (c *RedisCache) IsDuplicate(key, metricName string) bool {
	conf, exists := defaultCacheConfig[metricName]
	if !exists || !conf.EnableDedup {
		return false // 该指标不需要去重
	}

	conn := c.Pool.Get()
	defer conn.Close()

	vmKey := fmt.Sprintf("vm:%s:%s", metricName, key)
	exists, _ = redis.Bool(conn.Do("EXISTS", vmKey))
	if exists {
		return true // 已存在
	}

	// 设置过期时间
	_, _ = conn.Do("SETEX", vmKey, int(conf.ExpireTime.Seconds()), "1")
	return false
}
