package common

import (
	"context"
	"engine/common/xerr"
	"fmt"
	"io/ioutil"
	"math"
	"math/rand"
	"net/http"
	"time"
)

func InSlice(str string, s []string) bool {
	for _, v := range s {
		if str == v {
			return true
		}
	}
	return false
}

func InIntSlice(str int, s []int) bool {
	for _, v := range s {
		if str == v {
			return true
		}
	}
	return false
}

func InInterfaceSlice(str interface{}, s []interface{}) bool {
	for _, v := range s {
		if str == v {
			return true
		}
	}
	return false
}

func InInt64Slice(str int64, s []int64) bool {
	for _, v := range s {
		if str == v {
			return true
		}
	}
	return false
}

func ShuffleInt64Slice(slice []int64) []int64 {
	rand.Seed(time.Now().UnixNano())
	rand.Shuffle(len(slice), func(i, j int) {
		slice[i], slice[j] = slice[j], slice[i]
	})
	return slice
}

// CalculateExclusivePrice 计算不含税金额
func CalculateExclusivePrice(inclusivePrice, taxRate float64, decimalPlaces int) float64 {
	exclusivePrice := inclusivePrice / (1 + taxRate)
	return math.Floor(exclusivePrice*math.Pow10(decimalPlaces)) / math.Pow10(decimalPlaces)
}

func TimeToString(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}

func TimeStampToString(t int64) string {
	if t == 0 {
		return ""
	}
	return time.Unix(t, 0).Format("2006-01-02 15:04:05")
}

// GetUserId 获取用户id
func GetUserId(ctx context.Context) (UserId int64, err error) {
	uid := ctx.Value("uid")
	if id, ok := uid.(int64); ok {
		return id, nil
	}
	return 0, xerr.NewErrCode(xerr.UserNotExist)
}

func AuctionFormatTime(inputTime string, conclusion string) string {
	layout := "2006-01-02 15:04:05"
	t, err := time.ParseInLocation(layout, inputTime, time.Local)
	if err != nil {
		return inputTime
	}

	currentTime := time.Now()
	startOfToday := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, currentTime.Location())
	startOfTomorrow := startOfToday.Add(24 * time.Hour)
	endOfTomorrow := startOfTomorrow.Add(24 * time.Hour)

	if t.Unix() >= startOfToday.Unix() && t.Unix() < startOfTomorrow.Unix() {
		return fmt.Sprintf("今天 %s %s", t.Format("15:04:05"), conclusion)
	} else if t.Unix() >= startOfTomorrow.Unix() && t.Unix() < endOfTomorrow.Unix() {
		return fmt.Sprintf("明天 %s %s", t.Format("15:04:05"), conclusion)
	} else {
		return inputTime + " " + conclusion
	}
}

func MergeInt64AndInterface(d []int64, s interface{}, Left bool) []interface{} {
	var result []interface{}
	if Left {
		result = append(result, s)
	}
	for _, i := range d {
		result = append(result, i)
	}
	if !Left {
		result = append(result, s)
	}
	return result
}

func SliceInt64ToInterface(old []int64) []interface{} {
	var d []interface{}
	for _, i := range old {
		d = append(d, i)
	}
	return d
}

func DifferenceForInt64(slice1, slice2 []int64) []int64 {
	// 创建一个 map，用于记录 slice1 中的元素
	set := make(map[int64]bool)

	// 将 slice1 中的元素添加到 set 中
	for _, num := range slice1 {
		set[num] = true
	}

	// 遍历 slice2，将在 set 中存在的元素从 set 中删除
	for _, num := range slice2 {
		delete(set, num)
	}

	// 创建一个切片，将 set 中剩余的元素添加到切片中
	difference := make([]int64, 0, len(set))
	for num := range set {
		difference = append(difference, num)
	}

	return difference
}

func DeleteSlice(slice []interface{}, elem interface{}) []interface{} {
	tgt := slice[:0]
	for _, v := range slice {
		if v != elem {
			tgt = append(tgt, v)
		}
	}
	return tgt
}

func GetImagesBytes(url string) ([]byte, error) {
	response, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()

	// 读取图片数据到一个字节切片
	return ioutil.ReadAll(response.Body)

}

// TruncationFloat64 截断并保留两位小数
func TruncationFloat64(value float64, n ...int) float64 {
	digits := 2 // 默认值
	if len(n) > 0 {
		digits = n[0]
	}
	scale := math.Pow10(digits)
	return math.Trunc(value*scale) / scale
}

// AddFloatWithPrecision 将两个浮点数转换为指定小数位数的整数后相加，再转换回浮点数
func AddFloatWithPrecision(a, b float64, n ...int) float64 {
	digits := 2 // 默认值
	if len(n) > 0 {
		digits = n[0]
	}
	// 计算转换因子
	factor := math.Pow10(digits)

	// 将浮点数转换为整数（乘以因子后四舍五入）
	aInt := int64(math.Round(a * factor))
	bInt := int64(math.Round(b * factor))

	// 整数相加
	sumInt := aInt + bInt

	// 转换回浮点数
	return float64(sumInt) / factor
}
