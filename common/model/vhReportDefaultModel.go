package model

import (
	"context"
	"database/sql"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ VhReportDefaultModel = (*customVhReportDefaultModel)(nil)
var ChannelName = map[int64]string{
	ReportChannelOpen:           "开屏",
	ReportChannelEject:          "弹窗",
	ReportChannelHome:           "首页",
	ReportChannelFlash:          "闪购",
	ReportChannelSecond:         "秒发",
	ReportChannelCommunity:      "社区",
	ReportChannelRabbit:         "兔头",
	ReportChannelOwn:            "个人中心",
	ReportChannelMerchantSecond: "商家秒发",
	ReportChannelNotice:         "消息推送",
	ReportChannelOther:          "其他",
}

type (
	// VhReportDefaultModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhReportDefaultModel.
	VhReportDefaultModel interface {
		vhReportDefaultModel
		GetTable() string
		Inserts(ctx context.Context, datas []*VhReportDefault) (sql.Result, error)
		FindRows(ctx context.Context, rowBuilder squirrel.SelectBuilder, resp interface{}) error
		CountBuilder(field string) squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, resp interface{}, page, pageSize int64) error
	}

	customVhReportDefaultModel struct {
		*defaultVhReportDefaultModel
	}

	VhReportChannelRegion struct {
		Channel  int64 `db:"channel"`   // 频道（0 开屏 1弹窗 2 首页 3 闪购 4 秒发 5 社区 6 兔头 7 个人中心 8 商家秒发）
		RegionId int64 `db:"region_id"` // 区域id
	}
	VhReportSummaryDay struct {
		Uid string `db:"uid"`
		H   int64  `db:"h"`
	}
	VhReportPvUvByDateRange struct {
		Date string `db:"date"`
		Pv   int64  `db:"pv"`
		Uv   int64  `db:"uv"`
	}
)

const (
	ReportGenreAdvert int64 = 1
	ReportGenreGoods  int64 = 2
	ReportGenreOther  int64 = 3

	ReportChannelOpen           int64 = 0
	ReportChannelEject          int64 = 1
	ReportChannelHome           int64 = 2
	ReportChannelFlash          int64 = 3
	ReportChannelSecond         int64 = 4
	ReportChannelCommunity      int64 = 5
	ReportChannelRabbit         int64 = 6
	ReportChannelOwn            int64 = 7
	ReportChannelMerchantSecond int64 = 8
	ReportChannelNotice         int64 = 9
	ReportChannelOther          int64 = 10

	ReportClientIos     int64 = 0
	ReportClientAndroid int64 = 1
	ReportClientMini    int64 = 2
	ReportClientH5      int64 = 3
	ReportClientPc      int64 = 4
)

// NewVhReportDefaultModel returns a model for the database table.
func NewVhReportDefaultModel(conn sqlx.SqlConn) VhReportDefaultModel {
	return &customVhReportDefaultModel{
		defaultVhReportDefaultModel: newVhReportDefaultModel(conn),
	}
}

func (m *customVhReportDefaultModel) Inserts(ctx context.Context, datas []*VhReportDefault) (sql.Result, error) {
	if len(datas) == 0 {
		return nil, nil
	}

	sq := squirrel.Insert(m.table).Columns(vhReportDefaultRowsExpectAutoSet)
	for _, data := range datas {
		sq = sq.Values(data.Uid, data.IsLogin, data.Genre, data.Channel, data.RegionId, data.ButtonId, data.Client, data.Mid, data.CreatedTime)
	}

	query, values, err := sq.ToSql()
	if err != nil {
		return nil, err
	}

	ret, err := m.conn.ExecCtx(ctx, query, values...)
	if err != nil {
		return nil, err
	}

	return ret, err
}

func (m *customVhReportDefaultModel) GetTable() string {
	return m.table
}
func (m *customVhReportDefaultModel) FindRows(ctx context.Context, rowBuilder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := rowBuilder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)
	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *customVhReportDefaultModel) CountBuilder(field string) squirrel.SelectBuilder {
	return squirrel.Select("COUNT(" + field + ") as count").From(m.table)
}

func (m *customVhReportDefaultModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64
	err = m.conn.QueryRowCtx(ctx, &count, query, values...)
	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *customVhReportDefaultModel) FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, resp interface{}, page, pageSize int64) error {
	offset := (page - 1) * pageSize

	query, values, err := rowBuilder.Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)
	switch err {
	case nil:
		return nil
	default:
		return err
	}
}
