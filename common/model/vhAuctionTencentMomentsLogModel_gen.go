// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhAuctionTencentMomentsLogFieldNames          = builder.RawFieldNames(&VhAuctionTencentMomentsLog{})
	vhAuctionTencentMomentsLogRows                = strings.Join(vhAuctionTencentMomentsLogFieldNames, ",")
	vhAuctionTencentMomentsLogRowsExpectAutoSet   = strings.Join(stringx.Remove(vhAuctionTencentMomentsLogFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhAuctionTencentMomentsLogRowsWithPlaceHolder = strings.Join(stringx.Remove(vhAuctionTencentMomentsLogFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhAuctionTencentMomentsLogModel interface {
		Insert(ctx context.Context, data *VhAuctionTencentMomentsLog) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*VhAuctionTencentMomentsLog, error)
		Update(ctx context.Context, data *VhAuctionTencentMomentsLog) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultVhAuctionTencentMomentsLogModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhAuctionTencentMomentsLog struct {
		Id         int64     `db:"id"`
		Uid        int64     `db:"uid"`
		AuctionId  int64     `db:"auction_id"`  // 拍品id
		Genre      int64     `db:"genre"`       // 类型：1参拍 2出价 3用户从朋友圈过来的
		CreateTime time.Time `db:"create_time"` // 创建时间
	}
)

func newVhAuctionTencentMomentsLogModel(conn sqlx.SqlConn) *defaultVhAuctionTencentMomentsLogModel {
	return &defaultVhAuctionTencentMomentsLogModel{
		conn:  conn,
		table: "`vh_auction_tencent_moments_log`",
	}
}

func (m *defaultVhAuctionTencentMomentsLogModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhAuctionTencentMomentsLogModel) FindOne(ctx context.Context, id int64) (*VhAuctionTencentMomentsLog, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhAuctionTencentMomentsLogRows, m.table)
	var resp VhAuctionTencentMomentsLog
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhAuctionTencentMomentsLogModel) Insert(ctx context.Context, data *VhAuctionTencentMomentsLog) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?)", m.table, vhAuctionTencentMomentsLogRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Uid, data.AuctionId, data.Genre)
	return ret, err
}

func (m *defaultVhAuctionTencentMomentsLogModel) Update(ctx context.Context, data *VhAuctionTencentMomentsLog) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhAuctionTencentMomentsLogRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Uid, data.AuctionId, data.Genre, data.Id)
	return err
}

func (m *defaultVhAuctionTencentMomentsLogModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhAuctionTencentMomentsLogRows).From(m.table)
}

func (m *defaultVhAuctionTencentMomentsLogModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhAuctionTencentMomentsLogModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhAuctionTencentMomentsLogModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhAuctionTencentMomentsLogModel) TableName() string {
	return m.table
}
