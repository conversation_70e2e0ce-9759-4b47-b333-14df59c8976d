// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhCouponIssueFieldNames          = builder.RawFieldNames(&VhCouponIssue{})
	vhCouponIssueRows                = strings.Join(vhCouponIssueFieldNames, ",")
	vhCouponIssueRowsExpectAutoSet   = strings.Join(stringx.Remove(vhCouponIssueFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhCouponIssueRowsWithPlaceHolder = strings.Join(stringx.Remove(vhCouponIssueFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhCouponIssueModel interface {
		Insert(ctx context.Context, data *VhCouponIssue) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*VhCouponIssue, error)
		Update(ctx context.Context, data *VhCouponIssue) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultVhCouponIssueModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhCouponIssue struct {
		Id                     int64          `db:"id"`                        // ID
		Uid                    int64          `db:"uid"`                       // 用户ID
		SubOrderNo             sql.NullString `db:"sub_order_no"`              // 子订单编号
		CouponSn               string         `db:"coupon_sn"`                 // 唯一的编号
		CouponId               int64          `db:"coupon_id"`                 // 优惠券ID
		ApplyUid               int64          `db:"apply_uid"`                 // 申请人
		IssueCause             sql.NullInt64  `db:"issue_cause"`               // 发券事由(原因)1顶赛漏液 2延迟发货 3外观破损4纠错鼓励 5质量问题 6其他 7券包发放
		ReviewStatus           sql.NullInt64  `db:"review_status"`             // 审核状态 1审核中 2已发放 3已使用 4已过期 5已驳回6申请失败7已作废
		IsDelete               int64          `db:"is_delete"`                 // 是否删除 1正常 2删除
		Remark                 sql.NullString `db:"remark"`                    // 备注
		Reason                 string         `db:"reason"`                    // 驳回原因
		ExpireTime             sql.NullInt64  `db:"expire_time"`               // 优惠券具体到期时间
		AuditTime              int64          `db:"audit_time"`                // 审核时间
		UsedTime               int64          `db:"used_time"`                 // 使用时间
		CreatedTime            int64          `db:"created_time"`              // 创建时间
		UpdateTime             int64          `db:"update_time"`               // 更新时间
		CouponPackageDetailsId sql.NullInt64  `db:"coupon_package_details_id"` // 优惠券包具体id
		ReturnId               sql.NullInt64  `db:"return_id"`                 // 退还的优惠券id
	}
)

func newVhCouponIssueModel(conn sqlx.SqlConn) *defaultVhCouponIssueModel {
	return &defaultVhCouponIssueModel{
		conn:  conn,
		table: "`vh_coupon_issue`",
	}
}

func (m *defaultVhCouponIssueModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhCouponIssueModel) FindOne(ctx context.Context, id int64) (*VhCouponIssue, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhCouponIssueRows, m.table)
	var resp VhCouponIssue
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhCouponIssueModel) Insert(ctx context.Context, data *VhCouponIssue) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, vhCouponIssueRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Uid, data.SubOrderNo, data.CouponSn, data.CouponId, data.ApplyUid, data.IssueCause, data.ReviewStatus, data.IsDelete, data.Remark, data.Reason, data.ExpireTime, data.AuditTime, data.UsedTime, data.CreatedTime, data.CouponPackageDetailsId, data.ReturnId)
	return ret, err
}

func (m *defaultVhCouponIssueModel) Update(ctx context.Context, data *VhCouponIssue) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhCouponIssueRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Uid, data.SubOrderNo, data.CouponSn, data.CouponId, data.ApplyUid, data.IssueCause, data.ReviewStatus, data.IsDelete, data.Remark, data.Reason, data.ExpireTime, data.AuditTime, data.UsedTime, data.CreatedTime, data.CouponPackageDetailsId, data.ReturnId, data.Id)
	return err
}

func (m *defaultVhCouponIssueModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhCouponIssueRows).From(m.table)
}

func (m *defaultVhCouponIssueModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhCouponIssueModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhCouponIssueModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhCouponIssueModel) TableName() string {
	return m.table
}
