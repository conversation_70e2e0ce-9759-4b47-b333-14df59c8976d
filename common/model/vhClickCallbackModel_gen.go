// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhClickCallbackFieldNames          = builder.RawFieldNames(&VhClickCallback{})
	vhClickCallbackRows                = strings.Join(vhClickCallbackFieldNames, ",")
	vhClickCallbackRowsExpectAutoSet   = strings.Join(stringx.Remove(vhClickCallbackFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhClickCallbackRowsWithPlaceHolder = strings.Join(stringx.Remove(vhClickCallbackFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhClickCallbackModel interface {
		Insert(ctx context.Context, data *VhClickCallback) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*VhClickCallback, error)
		Update(ctx context.Context, data *VhClickCallback) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultVhClickCallbackModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhClickCallback struct {
		Id         int64     `db:"id"`
		Os         int64     `db:"os"`       // 0安卓1苹果
		Genre      int64     `db:"genre"`    // 0抖音1百度
		Oaid       string    `db:"oaid"`     // 安卓oaid
		Imei       string    `db:"imei"`     // 安卓imei
		Idfa       string    `db:"idfa"`     // 苹果idfa
		Callback   string    `db:"callback"` // 点击给到的callback地址
		Akey       string    `db:"akey"`     // click加密akey
		CreateTime time.Time `db:"create_time"`
	}
)

func newVhClickCallbackModel(conn sqlx.SqlConn) *defaultVhClickCallbackModel {
	return &defaultVhClickCallbackModel{
		conn:  conn,
		table: "`vh_click_callback`",
	}
}

func (m *defaultVhClickCallbackModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhClickCallbackModel) FindOne(ctx context.Context, id int64) (*VhClickCallback, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhClickCallbackRows, m.table)
	var resp VhClickCallback
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhClickCallbackModel) Insert(ctx context.Context, data *VhClickCallback) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?)", m.table, vhClickCallbackRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Os, data.Genre, data.Oaid, data.Imei, data.Idfa, data.Callback, data.Akey)
	return ret, err
}

func (m *defaultVhClickCallbackModel) Update(ctx context.Context, data *VhClickCallback) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhClickCallbackRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Os, data.Genre, data.Oaid, data.Imei, data.Idfa, data.Callback, data.Akey, data.Id)
	return err
}

func (m *defaultVhClickCallbackModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhClickCallbackRows).From(m.table)
}

func (m *defaultVhClickCallbackModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhClickCallbackModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhClickCallbackModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhClickCallbackModel) TableName() string {
	return m.table
}
