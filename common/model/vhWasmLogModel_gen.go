// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhWasmLogFieldNames          = builder.RawFieldNames(&VhWasmLog{})
	vhWasmLogRows                = strings.Join(vhWasmLogFieldNames, ",")
	vhWasmLogRowsExpectAutoSet   = strings.Join(stringx.Remove(vhWasmLogFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhWasmLogRowsWithPlaceHolder = strings.Join(stringx.Remove(vhWasmLogFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhWasmLogModel interface {
		Insert(ctx context.Context, data *VhWasmLog) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*VhWasmLog, error)
		Update(ctx context.Context, data *VhWasmLog) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultVhWasmLogModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhWasmLog struct {
		Id         int64     `db:"id"`
		UserAgent  string    `db:"user_agent"`
		CreateTime time.Time `db:"create_time"`
	}
)

func newVhWasmLogModel(conn sqlx.SqlConn) *defaultVhWasmLogModel {
	return &defaultVhWasmLogModel{
		conn:  conn,
		table: "`vh_wasm_log`",
	}
}

func (m *defaultVhWasmLogModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhWasmLogModel) FindOne(ctx context.Context, id int64) (*VhWasmLog, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhWasmLogRows, m.table)
	var resp VhWasmLog
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhWasmLogModel) Insert(ctx context.Context, data *VhWasmLog) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?)", m.table, vhWasmLogRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.UserAgent)
	return ret, err
}

func (m *defaultVhWasmLogModel) Update(ctx context.Context, data *VhWasmLog) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhWasmLogRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.UserAgent, data.Id)
	return err
}

func (m *defaultVhWasmLogModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhWasmLogRows).From(m.table)
}

func (m *defaultVhWasmLogModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhWasmLogModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhWasmLogModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhWasmLogModel) TableName() string {
	return m.table
}
