package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ VhAuctionTencentMomentsLogModel = (*customVhAuctionTencentMomentsLogModel)(nil)

type (
	// VhAuctionTencentMomentsLogModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhAuctionTencentMomentsLogModel.
	VhAuctionTencentMomentsLogModel interface {
		vhAuctionTencentMomentsLogModel
		InsertWithCreateTime(ctx context.Context, data *VhAuctionTencentMomentsLog) (sql.Result, error)
	}

	customVhAuctionTencentMomentsLogModel struct {
		*defaultVhAuctionTencentMomentsLogModel
	}
)

// NewVhAuctionTencentMomentsLogModel returns a model for the database table.
func NewVhAuctionTencentMomentsLogModel(conn sqlx.SqlConn) VhAuctionTencentMomentsLogModel {
	return &customVhAuctionTencentMomentsLogModel{
		defaultVhAuctionTencentMomentsLogModel: newVhAuctionTencentMomentsLogModel(conn),
	}
}

func (m *defaultVhAuctionTencentMomentsLogModel) InsertWithCreateTime(ctx context.Context, data *VhAuctionTencentMomentsLog) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s, create_time) values (?, ?, ?, ?)", m.table, vhAuctionTencentMomentsLogRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Uid, data.AuctionId, data.Genre, data.CreateTime)
	return ret, err
}
