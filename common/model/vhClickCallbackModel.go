package model

import (
	"context"
	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ VhClickCallbackModel = (*customVhClickCallbackModel)(nil)

const (
	GenreByte  = 0
	GenreBaidu = 1

	GenreHuawei = 2
)

type (
	// VhClickCallbackModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhClickCallbackModel.
	VhClickCallbackModel interface {
		vhClickCallbackModel
		FindOneCustom(ctx context.Context, builder squirrel.SelectBuilder) (*VhClickCallback, error)
	}

	customVhClickCallbackModel struct {
		*defaultVhClickCallbackModel
	}
)

// NewVhClickCallbackModel returns a model for the database table.
func NewVhClickCallbackModel(conn sqlx.SqlConn) VhClickCallbackModel {
	return &customVhClickCallbackModel{
		defaultVhClickCallbackModel: newVhClickCallbackModel(conn),
	}
}

func (m *defaultVhClickCallbackModel) FindOneCustom(ctx context.Context, builder squirrel.SelectBuilder) (*VhClickCallback, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	var resp VhClickCallback
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}
