package auction

import (
	"encoding/json"
	"engine/api/internal/logic/auction"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"io/ioutil"
	"net/http"
)

func TencentMomentsLogsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetawayLogReq
		if all, err := ioutil.ReadAll(r.Body); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		} else {
			if err := json.Unmarshal(all, &req); err != nil {
				result.ParamErrorResult(r, w, err)
				return
			}
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := auction.NewTencentMomentsLogsLogic(r.<PERSON>(), svcCtx)
		err := l.TencentMomentsLogs(&req)
		result.HttpResult(r, w, result.NullJson{}, err)
	}
}
