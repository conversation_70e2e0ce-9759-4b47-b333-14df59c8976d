// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"

	auction "engine/api/internal/handler/auction"
	baidu "engine/api/internal/handler/baidu"
	buriedPointConfig "engine/api/internal/handler/buriedPointConfig"
	byteDance "engine/api/internal/handler/byteDance"
	huawei "engine/api/internal/handler/huawei"
	report "engine/api/internal/handler/report"
	silentStatistics "engine/api/internal/handler/silentStatistics"
	tencentAd "engine/api/internal/handler/tencentAd"
	"engine/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/report",
					Handler: report.ReportHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/ReportChannelRegionList",
					Handler: report.ReportChannelRegionListHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/ReportButtonByRegionList",
					Handler: report.ReportButtonByRegionListHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/ReportSummaryByDay",
					Handler: report.ReportSummaryByDayHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/ReportSummaryByDayExcel",
					Handler: report.ReportSummaryByDayExcelHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/GetPvUvByDateRange",
					Handler: report.GetPvUvByDateRangeHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/wasmReport",
					Handler: report.WasmReportHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/ReportSummary",
					Handler: report.ReportSummaryHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/ReportSummaryExcel",
					Handler: report.ReportSummaryExcelHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/h5Report",
					Handler: report.H5ReportHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/vmProductReport",
					Handler: report.VmProductReportHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/vmAnyReport",
					Handler: report.VmAnyReportHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/maidian/v3/report"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/miniReport",
				Handler: tencentAd.MiniReportHandler(serverCtx),
			},
		},
		rest.WithPrefix("/maidian/v3/tencentAd"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.User},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/registerUidByTencentMoments",
					Handler: auction.RegisterUidByTencentMomentsHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/maidian/v3/auction"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/tencentMomentsLogs",
				Handler: auction.TencentMomentsLogsHandler(serverCtx),
			},
		},
		rest.WithPrefix("/maidian/v3/auction"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/callback",
				Handler: byteDance.CallbackHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/report",
				Handler: byteDance.ReportHandler(serverCtx),
			},
		},
		rest.WithPrefix("/maidian/v3/byteDance"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/callback",
				Handler: huawei.CallbackHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/report",
				Handler: huawei.ReportHandler(serverCtx),
			},
		},
		rest.WithPrefix("/maidian/v3/huawei"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/callback",
				Handler: baidu.CallbackHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/report",
				Handler: baidu.ReportHandler(serverCtx),
			},
		},
		rest.WithPrefix("/maidian/v3/baidu"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/orderReport",
				Handler: silentStatistics.OrderReportHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/report",
				Handler: silentStatistics.ReportHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/list",
				Handler: silentStatistics.ListHandler(serverCtx),
			},
		},
		rest.WithPrefix("/maidian/v3/silentStatistics"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/add",
				Handler: buriedPointConfig.AddBuriedPointConfigHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/edit",
				Handler: buriedPointConfig.EditBuriedPointConfigHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/list",
				Handler: buriedPointConfig.BuriedPointConfigListHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/channel",
				Handler: buriedPointConfig.BuriedPointConfigChannelHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/genre",
				Handler: buriedPointConfig.BuriedPointConfigGenreHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/filter",
				Handler: buriedPointConfig.BuriedPointConfigFilterHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/filterbutton",
				Handler: buriedPointConfig.BuriedPointConfigFilterButtonHandler(serverCtx),
			},
		},
		rest.WithPrefix("/maidian/v3/buriedPointConfig"),
	)
}
