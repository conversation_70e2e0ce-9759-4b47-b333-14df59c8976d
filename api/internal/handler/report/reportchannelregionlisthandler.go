package report

import (
	"engine/api/internal/logic/report"
	"engine/api/internal/svc"
	"engine/common/result"
	"net/http"
)

func ReportChannelRegionListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := report.NewReportChannelRegionListLogic(r.Context(), svcCtx)
		resp, err := l.ReportChannelRegionList()
		result.HttpResult(r, w, resp, err)
	}
}
