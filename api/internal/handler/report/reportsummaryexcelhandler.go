package report

import (
	"engine/api/internal/logic/report"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"fmt"
	"net/http"

	"github.com/tealeg/xlsx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func ReportSummaryExcelHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ReportSummaryReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := report.NewReportSummaryExcelLogic(r.Context(), svcCtx)
		resp, err := l.ReportSummaryExcel(&req)
		if err != nil {
			result.HttpResult(r, w, resp, err)
			return
		}

		// 创建Excel文件
		file := xlsx.NewFile()

		// 创建两个sheet
		pvSheet, _ := file.AddSheet("PV")
		uvSheet, _ := file.AddSheet("UV")

		// 设置列宽
		for _, sheet := range []*xlsx.Sheet{pvSheet, uvSheet} {
			sheet.SetColWidth(0, 0, 15) // 日期列宽
			for i := 1; i < len(resp.Header); i++ {
				sheet.SetColWidth(i, i, 12) // 数据列宽
			}
		}

		// 添加表头
		pvRow := pvSheet.AddRow()
		uvRow := uvSheet.AddRow()
		for _, header := range resp.Header {
			pvCell := pvRow.AddCell()
			pvCell.Value = header
			uvCell := uvRow.AddCell()
			uvCell.Value = header
		}

		// 添加PV数据
		for _, pvRow := range resp.PV {
			excelRow := pvSheet.AddRow()
			for _, value := range pvRow.([]interface{}) {
				excelCell := excelRow.AddCell()
				excelCell.Value = fmt.Sprintf("%v", value)
			}
		}

		// 添加UV数据
		for _, uvRow := range resp.UV {
			excelRow := uvSheet.AddRow()
			for _, value := range uvRow.([]interface{}) {
				excelCell := excelRow.AddCell()
				excelCell.Value = fmt.Sprintf("%v", value)
			}
		}

		//输出
		filename := fmt.Sprintf("%s-%s 埋点数据汇总.xlsx", req.StartDate, req.EndDate)
		_ = file.Write(w)
		w.Header().Set("Content-Type", "application/octet-stream")
		w.Header().Set("Content-Disposition", "attachment; filename="+filename)
		w.Header().Set("Content-Transfer-Encoding", "binary")

		w.WriteHeader(http.StatusOK)
	}
}
