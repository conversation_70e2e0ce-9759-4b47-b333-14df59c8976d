package report

import (
	"engine/api/internal/logic/report"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"net/http"
	"strconv"

	"github.com/tealeg/xlsx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func ReportSummaryByDayExcelHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ReportSummaryByDayReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := report.NewReportSummaryByDayExcelLogic(r.Context(), svcCtx)
		resp, err := l.ReportSummaryByDayExcel(&req)
		if err != nil {
			result.HttpResult(r, w, resp, err)
		}

		//数据处理
		file := xlsx.NewFile()
		sheet, _ := file.AddSheet("sheet")

		titles := []string{"小时", "pv", "uv"}
		row := sheet.AddRow()
		var cell *xlsx.Cell
		for _, title := range titles {
			cell = row.AddCell()
			cell.Value = title
		}

		values := [][]string{
			{
				"0",
				strconv.FormatInt(resp.One.Pv, 10),
				strconv.FormatInt(resp.One.Uv, 10),
			},
			{
				"1",
				strconv.FormatInt(resp.Two.Pv, 10),
				strconv.FormatInt(resp.Two.Uv, 10),
			},
			{
				"2",
				strconv.FormatInt(resp.Three.Pv, 10),
				strconv.FormatInt(resp.Three.Uv, 10),
			},
			{
				"3",
				strconv.FormatInt(resp.Four.Pv, 10),
				strconv.FormatInt(resp.Four.Uv, 10),
			},
			{
				"4",
				strconv.FormatInt(resp.Five.Pv, 10),
				strconv.FormatInt(resp.Five.Uv, 10),
			},
			{
				"5",
				strconv.FormatInt(resp.Six.Pv, 10),
				strconv.FormatInt(resp.Six.Uv, 10),
			},
			{
				"6",
				strconv.FormatInt(resp.Seven.Pv, 10),
				strconv.FormatInt(resp.Seven.Uv, 10),
			},
			{
				"7",
				strconv.FormatInt(resp.Eight.Pv, 10),
				strconv.FormatInt(resp.Eight.Uv, 10),
			},
			{
				"8",
				strconv.FormatInt(resp.Nine.Pv, 10),
				strconv.FormatInt(resp.Nine.Uv, 10),
			},
			{
				"9",
				strconv.FormatInt(resp.Ten.Pv, 10),
				strconv.FormatInt(resp.Ten.Uv, 10),
			},
			{
				"10",
				strconv.FormatInt(resp.Eleven.Pv, 10),
				strconv.FormatInt(resp.Eleven.Uv, 10),
			},
			{
				"11",
				strconv.FormatInt(resp.Twelve.Pv, 10),
				strconv.FormatInt(resp.Twelve.Uv, 10),
			},
			{
				"12",
				strconv.FormatInt(resp.Thirteen.Pv, 10),
				strconv.FormatInt(resp.Thirteen.Uv, 10),
			},
			{
				"13",
				strconv.FormatInt(resp.Fourteen.Pv, 10),
				strconv.FormatInt(resp.Fourteen.Uv, 10),
			},
			{
				"14",
				strconv.FormatInt(resp.Fifteen.Pv, 10),
				strconv.FormatInt(resp.Fifteen.Uv, 10),
			},
			{
				"15",
				strconv.FormatInt(resp.Sixteen.Pv, 10),
				strconv.FormatInt(resp.Sixteen.Uv, 10),
			},
			{
				"16",
				strconv.FormatInt(resp.Seventeen.Pv, 10),
				strconv.FormatInt(resp.Seventeen.Uv, 10),
			},
			{
				"17",
				strconv.FormatInt(resp.Eighteen.Pv, 10),
				strconv.FormatInt(resp.Eighteen.Uv, 10),
			},
			{
				"18",
				strconv.FormatInt(resp.Nineteen.Pv, 10),
				strconv.FormatInt(resp.Nineteen.Uv, 10),
			},
			{
				"19",
				strconv.FormatInt(resp.Twenty.Pv, 10),
				strconv.FormatInt(resp.Twenty.Uv, 10),
			},
			{
				"20",
				strconv.FormatInt(resp.TwentyOne.Pv, 10),
				strconv.FormatInt(resp.TwentyOne.Uv, 10),
			},
			{
				"21",
				strconv.FormatInt(resp.TwentyTwo.Pv, 10),
				strconv.FormatInt(resp.TwentyTwo.Uv, 10),
			},
			{
				"22",
				strconv.FormatInt(resp.TwentyThree.Pv, 10),
				strconv.FormatInt(resp.TwentyThree.Uv, 10),
			},
			{
				"23",
				strconv.FormatInt(resp.TwentyFour.Pv, 10),
				strconv.FormatInt(resp.TwentyFour.Uv, 10),
			},
		}

		for _, value := range values {
			row = sheet.AddRow()
			for _, s := range value {
				cell = row.AddCell()
				cell.Value = s
			}
		}

		//输出
		filename := req.Date + "日访问统计" + ".xlsx"
		_ = file.Write(w)
		w.Header().Set("Content-Type", "application/octet-stream")
		w.Header().Set("Content-Disposition", "attachment; filename="+filename)
		w.Header().Set("Content-Transfer-Encoding", "binary")

		w.WriteHeader(http.StatusOK)
	}
}
