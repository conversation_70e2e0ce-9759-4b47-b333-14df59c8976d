package buriedPointConfig

import (
	"engine/common/result"
	"net/http"

	"engine/api/internal/logic/buriedPointConfig"
	"engine/api/internal/svc"
)

func BuriedPointConfigFilterHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := buriedPointConfig.NewBuriedPointConfigFilterLogic(r.Context(), svcCtx)
		resp, err := l.BuriedPointConfigFilter()
		result.HttpResult(r, w, resp, err)
	}
}
