package buriedPointConfig

import (
	"engine/common/result"
	"net/http"

	"engine/api/internal/logic/buriedPointConfig"
	"engine/api/internal/svc"
)

func BuriedPointConfigChannelHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := buriedPointConfig.NewBuriedPointConfigChannelLogic(r.Context(), svcCtx)
		resp, err := l.BuriedPointConfigChannel()
		result.HttpResult(r, w, resp, err)
	}
}
