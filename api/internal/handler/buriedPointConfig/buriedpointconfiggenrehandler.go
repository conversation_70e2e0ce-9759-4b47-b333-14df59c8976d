package buriedPointConfig

import (
	"engine/common/result"
	"net/http"

	"engine/api/internal/logic/buriedPointConfig"
	"engine/api/internal/svc"
)

func BuriedPointConfigGenreHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := buriedPointConfig.NewBuriedPointConfigGenreLogic(r.Context(), svcCtx)
		resp, err := l.BuriedPointConfigGenre()
		result.HttpResult(r, w, resp, err)
	}
}
