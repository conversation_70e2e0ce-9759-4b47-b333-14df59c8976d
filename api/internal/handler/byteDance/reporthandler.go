package byteDance

import (
	"context"
	"engine/api/internal/logic/byteDance"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"github.com/spf13/cast"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func ReportHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ByteDanceReportReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		uid := cast.ToInt64(r.Header.Get("vinehoo-uid"))
		r = r.WithContext(context.WithValue(r.Context(), "uid", uid))

		l := byteDance.NewReportLogic(r.Context(), svcCtx)
		err := l.Report(&req)
		result.HttpResult(r, w, result.NullJson{}, err)
	}
}
