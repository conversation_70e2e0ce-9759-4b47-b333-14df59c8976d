// Code generated by goctl. DO NOT EDIT.
package types

type ReportDefaultReq struct {
	Data []*Default `json:"data" validate:"len=1" v:"数据"`
}

type Default struct {
	Uid         string `json:"uid" validate:"required" v:"用户id"`
	IsLogin     int64  `json:"is_login" validate:"min=0,max=1" v:"是否登陆"`
	Genre       int64  `json:"genre" validate:"min=1,max=3" v:"类型"`
	Channel     int64  `json:"channel" validate:"min=0,max=10" v:"频道"`
	RegionId    int64  `json:"region_id" validate:"required" v:"区域id"`
	ButtonId    int64  `json:"button_id" validate:"min=0" v:"按钮id"`
	Client      int64  `json:"client" validate:"min=0,max=4" v:"客户端"`
	Mid         int64  `json:"mid,optional" v:"商户id"`
	CreatedTime int64  `json:"created_time" validate:"min=1667974216,timeLt" v:"收集时间戳"`
}

type ReportChannelRegionListResp struct {
	Channels []interface{} `json:"channels"`
}

type ReportChannelRegionInfo struct {
	ChannelId   int64               `json:"channelId"`
	ChannelName string              `json:"channelName"`
	Regions     []*ReportRegionInfo `json:"regions"`
}

type ReportRegionInfo struct {
	RegionsId int64 `json:"regionsId"`
}

type ReportButtonByRegionListReq struct {
	ChannelId int64 `form:"channelId" validate:"min=0" v:"频道id"`
	RegionsId int64 `form:"regionsId" validate:"min=1" v:"区域id"`
	Page      int64 `form:"page" validate:"min=1" v:"分页"`
	Limit     int64 `form:"limit" validate:"min=5" v:"分页数量"`
}

type ReportButtonByRegionListResp struct {
	Buttons []interface{} `json:"buttons"`
	Total   int64         `json:"total"`
}

type ReportButtonInfo struct {
	ButtonId int64 `json:"buttonId"`
}

type ReportSummaryByDayReq struct {
	Date      string `form:"date" validate:"required" v:"日期"`
	ChannelId int64  `form:"channelId,min:0"`
	RegionId  int64  `form:"regionId,optional"`
	ButtonId  int64  `form:"buttonId,optional"`
}

type ReportSummaryByDayResp struct {
	One         ReportPUv `json:"0-1"`
	Two         ReportPUv `json:"1-2"`
	Three       ReportPUv `json:"2-3"`
	Four        ReportPUv `json:"3-4"`
	Five        ReportPUv `json:"4-5"`
	Six         ReportPUv `json:"5-6"`
	Seven       ReportPUv `json:"6-7"`
	Eight       ReportPUv `json:"7-8"`
	Nine        ReportPUv `json:"8-9"`
	Ten         ReportPUv `json:"9-10"`
	Eleven      ReportPUv `json:"10-11"`
	Twelve      ReportPUv `json:"11-12"`
	Thirteen    ReportPUv `json:"12-13"`
	Fourteen    ReportPUv `json:"13-14"`
	Fifteen     ReportPUv `json:"14-15"`
	Sixteen     ReportPUv `json:"15-16"`
	Seventeen   ReportPUv `json:"16-17"`
	Eighteen    ReportPUv `json:"17-18"`
	Nineteen    ReportPUv `json:"18-19"`
	Twenty      ReportPUv `json:"19-20"`
	TwentyOne   ReportPUv `json:"20-21"`
	TwentyTwo   ReportPUv `json:"21-22"`
	TwentyThree ReportPUv `json:"22-23"`
	TwentyFour  ReportPUv `json:"23-24"`
}

type GetPvUvByDateRangeReq struct {
	StartDate string `form:"start_date" validate:"required" v:"开始日期"`
	EndDate   string `form:"end_date" validate:"required" v:"结束日期"`
	ChannelId int64  `form:"channelId,min:0"`
	RegionId  int64  `form:"regionId,optional"`
	ButtonId  int64  `form:"buttonId,optional"`
}

type GetPvUvByDateRangeResp struct {
	List []GetPvUvByDateRangeInfo `json:"list"`
}

type GetPvUvByDateRangeInfo struct {
	Date string `json:"date"`
	ReportPUv
}

type ReportPUv struct {
	Pv int64 `json:"pv"`
	Uv int64 `json:"uv"`
}

type WasmReportReq struct {
	UserAgent string `json:"user_agent,optional"`
}

type ReportSummaryReq struct {
	StartDate string `form:"start_date" validate:"required" v:"开始日期"`
	EndDate   string `form:"end_date" validate:"required" v:"结束日期"`
	ChannelId int64  `form:"channelId,optional" v:"频道id"`
	RegionId  int64  `form:"regionId,optional" v:"区域id"`
	ButtonId  string `form:"buttonId,optional" v:"按钮id"`
}

type ReportSummaryResp struct {
	Header []string      `json:"header"`
	PV     []interface{} `json:"PV"`
	UV     []interface{} `json:"UV"`
}

type H5ReportReq struct {
	Url       string `json:"url"`
	ErrorInfo string `json:"error_info"`
}

type VmProductReportReq struct {
	Data []*VmProduct `json:"data" validate:"gte=1" v:"数据"`
}

type VmProduct struct {
	MetricName  string `json:"metric_name" validate:"required" v:"指标名"`
	Period      int64  `json:"period" validate:"required" v:"期数"`
	PeriodType  int64  `json:"period_type" validate:"min=0,max=3" v:"频道"`
	Uid         string `json:"uid" validate:"required" v:"用户id"`
	Device      string `json:"device" validate:"required" v:"设备"`
	CreatedTime int64  `json:"created_time" validate:"min=1667974216,timeLt" v:"收集时间戳"`
}

type VmAnyReportReq struct {
}

type TencentAdMiniReportReq struct {
	CreatedTime int64                     `json:"created_time" validate:"min=1667974216,timeLt" v:"收集时间戳"`
	OpenId      string                    `json:"openid" validate:"required" v:"用户openid"`
	ActionType  string                    `json:"action_type" validate:"required" v:"用户行为"`
	ClickId     string                    `json:"click_id" validate:"required" v:"clickId"`
	Source      int64                     `json:"source,default=1"`
	Order       *TencentAdMiniReportOrder `json:"order,optional" validate:"required_if=ActionType COMPLETE_ORDER" v:"订单信息"`
}

type TencentAdMiniReportOrder struct {
	Quantity int64 `json:"quantity" validate:"min=1" v:"单量"`
	Value    int64 `json:"value" validate:"min=1" v:"金额"`
}

type GetawayLogReq struct {
	ModuleName           string      `json:"module_name"`
	RequestMethod        string      `json:"request_method"`
	Host                 string      `json:"host"`
	RouteURL             string      `json:"route_url"`
	RequestClient        string      `json:"request_client"`
	RequestClientVersion interface{} `json:"request_client_version"`
	RequestParam         string      `json:"request_param"`
	RequestBody          string      `json:"request_body"`
	UserAgent            string      `json:"user_agent"`
	IP                   string      `json:"ip"`
	DingtalkDeptID       string      `json:"dingtalk_dept_id"`
	DingtalkUID          string      `json:"dingtalk_uid"`
	UID                  string      `json:"uid"`
	AccessToken          string      `json:"access_token"`
	Logon                bool        `json:"logon"`
	VosName              string      `json:"vos_name"`
	Referer              string      `json:"referer"`
	ErrCode              int         `json:"err_code"`
	ErrMsg               string      `json:"err_msg"`
	RequestTime          string      `json:"request_time"`
	ResponseTime         string      `json:"response_time"`
}

type RegisterUidByTencentMomentsReq struct {
	Genre string `json:"genre,default=tencentMoments"`
}

type ByteDanceCallbackReq struct {
	Callback string `form:"callback"`
	Oaid     string `form:"oaid,optional"`  //安卓oaid
	Imei     string `form:"imei,optional"`  //安卓imei
	Idfa     string `form:"idfa,optional"`  //苹果idfa
	Os       int64  `form:"os,range=[0:1]"` //0安卓1苹果3其他
}

type ByteDanceReportReq struct {
	Oaid        string  `json:"oaid,optional"`  //安卓oaid
	Imei        string  `json:"imei,optional"`  //安卓imei
	Idfa        string  `json:"idfa,optional"`  //苹果idfa
	Os          int64   `json:"os,range=[0:1]"` //0安卓1苹果3其他
	EventType   string  `json:"event_type"`
	EventWeight float64 `json:"event_weight,default=0"`
}

type HuaweiCallbackReq struct {
	Callback string `form:"callback"`
	Oaid     string `form:"oaid,optional"` //安卓oaid IdType不为0时
	Imei     string `form:"imei,optional"` //安卓imei IdType为0时
}

type HuaweiReportReq struct {
	Oaid        string  `json:"oaid,optional"`  //安卓oaid
	Imei        string  `json:"imei,optional"`  //安卓imei
	Os          int64   `json:"os,range=[0:1]"` //0安卓1苹果3其他
	EventType   string  `json:"event_type"`
	EventWeight float64 `json:"event_weight,default=0"`
}

type BaiduCallbackReq struct {
	ImeiMd5     string `form:"imei_md5,optional"`
	Idfa        string `form:"idfa,optional"`
	Oaid        string `form:"oaid,optional"`
	Os          int64  `form:"os,optional"`
	CallbackUrl string `form:"callback_url,optional"`
	Akey        string `form:"akey"` //密钥
}

type BaiduReportReq struct {
	Oaid        string  `json:"oaid,optional"`  //安卓oaid
	Imei        string  `json:"imei,optional"`  //安卓imei
	Idfa        string  `json:"idfa,optional"`  //苹果idfa
	Os          int64   `json:"os,range=[0:1]"` //0安卓1苹果3其他
	EventType   string  `json:"event_type"`
	EventWeight float64 `json:"event_weight,default=0"`
}

type SilentStatisticsOrderReportReq struct {
	MainOrderNo string  `json:"main_order_no" validate:"required" v:"主订单号"`
	Uid         int64   `json:"uid" validate:"required" v:"用户id"`
	UseCouponId int64   `json:"use_coupon_id,default=0"`
	PayMoney    float64 `json:"pay_money" validate:"required" v:"支付金额"`
	PayTime     int64   `json:"pay_time" validate:"required" v:"支付时间"`
}

type SilentStatisticsReportReq struct {
	Genre int64 `json:"genre" validate:"min=1,max=3" v:"类型"` //1进入页面,2领劵,3添加客户
	Uid   int64 `json:"uid" validate:"required" v:"用户id"`
}

type SilentStatisticsListReq struct {
	Page            int64  `form:"page" validate:"min=1" v:"分页"`
	Limit           int64  `form:"limit" validate:"min=10" v:"分页数量"`
	PayTimeStart    int64  `form:"pay_time_start,optional" v:"支付月份开始时间"`
	PayTimeEnd      int64  `form:"pay_time_end,optional" v:"支付月份结束时间"`
	UserFilter      string `form:"user_filter,optional" v:"用户id/昵称"`
	PhoneWeFilter   string `form:"phone_we_filter,optional" v:"手机号/微信号"`
	CouponTimeStart int64  `form:"coupon_time_start,optional" v:"领劵开始时间"`
	CouponTimeEnd   int64  `form:"coupon_time_end,optional" v:"领劵结束时间"`
	IsAddCs         int64  `form:"is_add_cs,default=2" v:"是否添加客服"`
	Sort            int64  `form:"sort,default=3" v:"排序规则"` //1单量 2金额 3最后一次下单时间
}

type SilentStatisticsListResp struct {
	Total           int64                      `json:"total"` //总用户
	Pv              int64                      `json:"pv"`
	TotalOrderCt    int64                      `json:"total_order_ct"`    //总单量
	TotalOrderMoney float64                    `json:"total_order_money"` //总金额
	TotalAddCs      int64                      `json:"total_add_cs"`      //总添加客户量
	CsRate          int64                      `json:"cs_rate"`           //添加客服率
	TotalCoupon     int64                      `json:"total_coupon"`      //总领劵人数
	TotalUseCoupon  int64                      `json:"total_use_coupon"`  //总使用劵人数
	List            []SilentStatisticsListInfo `json:"list"`
}

type SilentStatisticsListInfo struct {
	Uid           int64   `json:"uid"`             //用户id
	Name          string  `json:"name"`            //昵称
	Phone         string  `json:"phone"`           //手机号
	WeUid         string  `json:"we_uid"`          //微信号
	IsAddCs       int64   `json:"is_add_cs"`       //是否添加客服 1是 0否
	WakeTime      string  `json:"wake_time"`       //唤醒时间
	CouponTime    string  `json:"coupon_time"`     //领劵时间
	LastBuyTime   string  `json:"last_buy_time"`   //最后下单时间
	OrderCt       int64   `json:"order_ct"`        //订单量
	SumOrderMoney float64 `json:"sum_order_money"` //订单总金额
}

type AddBuriedPointConfigReq struct {
	Genre      int64  `json:"genre" validate:"min=1" v:"类型"`
	Channel    int64  `json:"channel" validate:"min=0" v:"频道"`
	RegionId   int64  `json:"region_id" validate:"required" v:"区域id"`
	RegionName string `json:"region_name" validate:"required" v:"区域名称"`
}

type EditBuriedPointConfigReq struct {
	Id         int64  `json:"id" validate:"min=1" v:"配置ID"`
	Genre      int64  `json:"genre" validate:"min=1" v:"类型"`
	Channel    int64  `json:"channel" validate:"min=0" v:"频道"`
	RegionId   int64  `json:"region_id" validate:"required" v:"区域id"`
	RegionName string `json:"region_name" validate:"required" v:"区域名称"`
}

type BuriedPointConfigListReq struct {
	Page  int64 `form:"page,optional"`
	Limit int64 `form:"limit,optional"`
}

type BuriedPointConfigListResp struct {
	List  []BuriedPointConfigListItem `json:"list"`
	Total int64                       `json:"total"`
}

type BuriedPointConfigListItem struct {
	Id          int64  `json:"id"`
	Genre       int64  `json:"genre"`
	GenreName   string `json:"genre_name"`
	Channel     int64  `json:"channel"`
	ChannelName string `json:"channel_name"`
	RegionId    int64  `json:"region_id"`
	RegionName  string `json:"region_name"`
	CreatedTime string `json:"created_time"`
}

type BuriedPointConfigFilterResp struct {
	Channels []BuriedPointConfigFilterRespChannels `json:"channels"`
}

type BuriedPointConfigFilterRespChannels struct {
	ChannelId   int64                               `json:"channelId"`
	ChannelName string                              `json:"channelName"`
	Regions     []BuriedPointConfigFilterRespRegion `json:"regions"`
}

type BuriedPointConfigFilterRespRegion struct {
	RegionsId  int64  `json:"regionsId"`
	RegionName string `json:"regionName"`
}

type BuriedPointConfigFilterButtonReq struct {
	RegionsId int64 `form:"regionsId" validate:"required" v:"区域ID"`
}

type BuriedPointConfigFilterButtonResp struct {
	Button []BuriedPointConfigFilterRespButton `json:"button"`
}

type BuriedPointConfigFilterRespButton struct {
	ButtonId   int64  `json:"buttonId"`
	ButtonName string `json:"buttonName"`
}

type BuriedPointConfigGenreResp struct {
	Genres []BuriedPointConfigGenreRespGenre `json:"genres"`
}

type BuriedPointConfigGenreRespGenre struct {
	GenreId   int64  `json:"genreId"`
	GenreName string `json:"genreName"`
}

type BuriedPointConfigChannelResp struct {
	Channels []BuriedPointConfigChannelRespChannel `json:"channels"`
}

type BuriedPointConfigChannelRespChannel struct {
	ChannelId   int64  `json:"channelId"`
	ChannelName string `json:"channelName"`
}
