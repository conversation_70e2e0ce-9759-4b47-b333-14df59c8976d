package tencentAd

import (
	"context"
	"engine/common/tencentAdvert"
	"engine/common/xerr"
	modelT "github.com/tencentad/marketing-api-go-sdk/pkg/model"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type MiniReportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMiniReportLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MiniReportLogic {
	return &MiniReportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MiniReportLogic) MiniReport(req *types.TencentAdMiniReportReq) error {
	//验证支持的行为类型
	switch modelT.ActionType(req.ActionType) {
	case modelT.ActionType_COMPLETE_ORDER:
		break
	case modelT.ActionType_REGISTER:
		break
	case modelT.ActionType_PAGE_VIEW:
		break
	default:
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "不支持的行为类型")
	}

	var ads *tencentAdvert.Ads
	switch req.Source {
	case 1:
		ads = l.svcCtx.Ads
		break
	case 2:
		ads = l.svcCtx.ShAds
		break
	case 3:
		ads = l.svcCtx.Sh303Ads
		break
	case 4:
		ads = l.svcCtx.Sh399Ads
		break
	case 5:
		ads = l.svcCtx.Ads264
		break
	case 6:
		ads = l.svcCtx.Ads389
		break
	case 7:
		ads = l.svcCtx.Sh163Ads
		break
	case 8:
		ads = l.svcCtx.Sh439Ads
		break
	case 9:
		ads = l.svcCtx.Sh440Ads
		break

	default:
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "不支持的行为类型")
	}

	//腾讯广告行为数据
	acts := make([]modelT.UserAction, 0)
	act := modelT.UserAction{
		ActionTime: &req.CreatedTime,
		UserId: &modelT.ActionsUserId{
			WechatOpenid: &req.OpenId,
			WechatAppId:  &ads.Config.ReportV1WeMiniAppId,
		},
		ActionType: modelT.ActionType(req.ActionType),
		Trace:      &modelT.Trace{ClickId: &req.ClickId},
	}
	if modelT.ActionType(req.ActionType) == modelT.ActionType_COMPLETE_ORDER {
		act.ActionParam = map[string]interface{}{"quantity": req.Order.Quantity, "value": req.Order.Value}
	}
	acts = append(acts, act)

	err := ads.AddUserAction(l.ctx, &modelT.UserActionsAddRequest{
		AccountId:       &ads.Config.AccountId,
		UserActionSetId: &ads.Sets.MdWeb.UserActionSetId,
		Actions:         &acts,
	})
	if err != nil {
		l.Error(err.Error())
		return xerr.NewErrMsg(err.Error())
	}

	return nil
}

/*curl -X POST \
http://tracking.e.qq.com/conv \
-H 'Content-Type: application/json' \
-H 'cache-control: no-cache' \
-d '{
"actions":[
     {
               "action_time": **********,
               "user_id": {
                               "wechat_openid":"ogQjs0IczXOP_jOcSFri_RCHTLA0",
                               "wechat_app_id":"wx3e0b582d1f902659"
               },
               "action_type": "REGISTER",
               "trace": {
                               "click_id":"wx07ly6vofeesk42"
               }
    }
]
}'*/
