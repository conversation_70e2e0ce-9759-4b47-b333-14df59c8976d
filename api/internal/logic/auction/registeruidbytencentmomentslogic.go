package auction

import (
	"context"
	logic "engine/api/internal"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"
	"fmt"
	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/logx"
)

type RegisterUidByTencentMomentsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRegisterUidByTencentMomentsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RegisterUidByTencentMomentsLogic {
	return &RegisterUidByTencentMomentsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RegisterUidByTencentMomentsLogic) RegisterUidByTencentMoments(req *types.RegisterUidByTencentMomentsReq) error {
	uid, err := logic.GetUserId(l.ctx)
	if err != nil {
		return err
	}

	count, err := l.svcCtx.TencentMomentsLogModel.FindCount(l.ctx, model.CountBuilder("id", l.svcCtx.TencentMomentsLogModel.TableName()).Where(squirrel.Eq{"uid": uid, "genre": 3}))
	if err != nil {
		logx.Error(fmt.Sprintf("RegisterUidByTencentMomentsLogic RegisterUidByTencentMoments TencentMomentsLogModel.FindCount err %e", err))
		return xerr.NewErrCode(xerr.DbError)
	}

	if count == 0 {
		_, err = l.svcCtx.TencentMomentsLogModel.Insert(l.ctx, &model.VhAuctionTencentMomentsLog{
			Uid:       uid,
			AuctionId: 0,
			Genre:     3,
		})

		if err != nil {
			logx.Error(fmt.Sprintf("RegisterUidByTencentMomentsLogic RegisterUidByTencentMoments TencentMomentsLogModel.Insert err %e", err))
			return xerr.NewErrCode(xerr.DbError)
		}
	}

	return nil
}
