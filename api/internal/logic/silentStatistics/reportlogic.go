package silentStatistics

import (
	"context"
	"engine/common/model"
	"engine/common/xerr"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ReportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewReportLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReportLogic {
	return &ReportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReportLogic) Report(req *types.SilentStatisticsReportReq) error {
	user, err := l.svcCtx.SilentStatisticsUser.FindOne(l.ctx, req.Uid)
	if err != nil && err != model.ErrNotFound {
		logx.Error("ReportLogic SilentStatisticsUser.FindOne err:" + err.<PERSON>rror())
		return xerr.NewErrCode(xerr.DbError)
	}

	//不在沉默用户里面的用户
	if user == nil {
		return nil
	}

	//1进入页面,2领劵,3添加客户
	var isUpdate bool
	tm := time.Now().Unix()
	switch req.Genre {
	case 1:
		/*//验证用户是否添加过激活时间
		if user.WakeTime == 0 {
			isUpdate = true
			user.WakeTime = tm
		}*/
		//添加访问pv
		_, err = l.svcCtx.SilentStatisticsPv.Insert(l.ctx, &model.VhSlientStatisticsPv{
			Genre:   1,
			Uid:     req.Uid,
			AddTime: tm,
		})
		if err != nil {
			logx.Error("ReportLogic SilentStatisticsPv.Insert err:" + err.Error())
			return xerr.NewErrCode(xerr.DbError)
		}
	case 2:
		if user.CouponTime == 0 {
			isUpdate = true
			user.CouponTime = tm
		}
	case 3:
		if user.IsAddCs == 0 {
			isUpdate = true
			user.IsAddCs = 1
		}
		//添加添加客服pv
		_, err = l.svcCtx.SilentStatisticsPv.Insert(l.ctx, &model.VhSlientStatisticsPv{
			Genre:   2,
			Uid:     req.Uid,
			AddTime: tm,
		})
		if err != nil {
			logx.Error("ReportLogic SilentStatisticsPv.Insert err:" + err.Error())
			return xerr.NewErrCode(xerr.DbError)
		}
	}

	if isUpdate {
		err = l.svcCtx.SilentStatisticsUser.Update(l.ctx, user)
		if err != nil {
			logx.Error("ReportLogic SilentStatisticsUser.Update err:" + err.Error())
			return xerr.NewErrCode(xerr.DbError)
		}
	}

	return nil
}
