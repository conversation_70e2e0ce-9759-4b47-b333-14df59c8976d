package silentStatistics

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
)

type OrderReportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewOrderReportLogic(ctx context.Context, svcCtx *svc.ServiceContext) *OrderReportLogic {
	return &OrderReportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *OrderReportLogic) OrderReport(req *types.SilentStatisticsOrderReportReq) error {
	user, err := l.svcCtx.SilentStatisticsUser.FindOne(l.ctx, req.Uid)
	if err != nil && err != model.ErrNotFound {
		logx.Error("OrderReportLogic SilentStatisticsUser.FindOne err:" + err.Error())
		return xerr.NewErrCode(xerr.DbError)
	}

	//不在沉默用户里面的用户 或者 已经下单超过三次
	if user == nil || user.BuyCt == 3 {
		return nil
	}

	user.BuyCt++
	user.LastBuyTime = req.PayTime

	if req.UseCouponId != 0 {
		//验证劵包
		var couponPackageId int64
		builder := squirrel.Select("COALESCE(p.coupon_package_id,0)").From(l.svcCtx.CouponIssueModel.TableName() + " c").
			LeftJoin("vh_coupon_package_details p on c.coupon_package_details_id = p.id").
			Where(squirrel.Eq{"c.id": req.UseCouponId})
		err = l.svcCtx.CouponIssueModel.FindCustom(l.ctx, builder, &couponPackageId)
		if err != nil && err != model.ErrNotFound {
			logx.Error("OrderReportLogic CouponIssueModel.FindCustom err:" + err.Error())
			return xerr.NewErrCode(xerr.DbError)
		}

		//首次使用沉默用户回归券时间
		if couponPackageId == 19 {
			if user.UseCouponTime == 0 {
				//首次使用优惠劵
				user.UseCouponTime = req.PayTime
				//唤醒
				user.WakeTime = req.PayTime
			}
		} else {
			req.UseCouponId = 0
		}
	}

	//如果还未唤醒就不算订单信息
	if user.WakeTime == 0 {
		return nil
	}

	//时间要求原因这里没有写成事物 如果后续有失败的情况可以调整
	err = l.svcCtx.SilentStatisticsUser.Update(l.ctx, user)
	if err != nil {
		logx.Error("OrderReportLogic SilentStatisticsUser.Update err:" + err.Error())
		return xerr.NewErrCode(xerr.DbError)
	}

	_, err = l.svcCtx.SilentStatisticsOrder.Insert(l.ctx, &model.VhSilentStatisticsOrder{
		MainOrderNo: req.MainOrderNo,
		Uid:         req.Uid,
		UseCouponId: req.UseCouponId,
		PayMoney:    req.PayMoney,
		PayTime:     req.PayTime,
	})
	if err != nil {
		logx.Error("OrderReportLogic SilentStatisticsOrder.Insert err:" + err.Error())
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
