package silentStatistics

import (
	"context"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"
	"golang.org/x/sync/errgroup"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

type ListInfoModel struct {
	model.VhSilentStatisticsUser
	MainOrderNo   string  `db:"main_order_no"`   // 主订单号
	UseCouponId   int64   `db:"use_coupon_id"`   // 优惠劵发放记录id(0代表没使用优惠劵)
	PayMoney      float64 `db:"pay_money"`       // 支付金额
	PayTime       int64   `db:"pay_time"`        // 支付时间
	OrderCt       int64   `db:"order_ct"`        // 订单量
	SumOrderMoney float64 `db:"sum_order_money"` // 总金额
}

type ListCountModel struct {
	TotalUser           int64   `db:"total_user"`             // 用户量
	TotalPurchaseCount  int64   `db:"total_purchase_count"`   // 总订单数
	TotalPurchaseMoney  float64 `db:"total_purchase_money"`   // 总订单金额
	TotalCsCount        int64   `db:"total_cs_count"`         // 总添加客服数
	Pv                  int64   `db:"pv"`                     // pv
	TotalCouponCount    int64   `db:"total_coupon_count"`     // 总领劵数
	TotalUseCouponCount int64   `db:"total_use_coupon_count"` // 总用劵数
}

func NewListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListLogic {
	return &ListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListLogic) List(req *types.SilentStatisticsListReq) (resp *types.SilentStatisticsListResp, err error) {
	resp = new(types.SilentStatisticsListResp)
	resp.List = make([]types.SilentStatisticsListInfo, 0)

	wait := &errgroup.Group{}

	var results []*ListInfoModel
	var counts ListCountModel
	pageBuilder := squirrel.Select("u.*", "o.main_order_no", "o.use_coupon_id", "o.pay_money", "o.pay_time").
		Column(squirrel.Expr("count(o.main_order_no) as order_ct")).
		Column(squirrel.Expr("sum(o.pay_money) as sum_order_money")).
		From(l.svcCtx.SilentStatisticsOrder.TableName() + " o").
		LeftJoin(l.svcCtx.SilentStatisticsUser.TableName() + " u ON o.uid = u.uid").
		GroupBy("o.uid")

	countSubBuilder := squirrel.Select("COUNT(o.main_order_no) AS ct", "SUM(o.pay_money) AS sumMoney", "o.uid",
		"u.is_add_cs", "u.coupon_time", "u.use_coupon_time").
		From(l.svcCtx.SilentStatisticsOrder.TableName() + " o").
		LeftJoin(l.svcCtx.SilentStatisticsUser.TableName() + " u ON o.uid = u.uid").
		GroupBy("o.uid")

	countBuilder := squirrel.Select("COUNT(uid) AS total_user").
		Column(squirrel.Expr("COALESCE(SUM(ct),0) as total_purchase_count")).      //总订单数
		Column(squirrel.Expr("COALESCE(SUM(sumMoney),0) as total_purchase_money")) //总订单金额

	//添加客户人数
	totalCsCountBuilder := squirrel.Select("count(id)").From(l.svcCtx.SilentStatisticsPv.TableName() + " p").LeftJoin(l.svcCtx.SilentStatisticsUser.TableName() + " u ON p.uid = u.uid").Where(squirrel.Eq{"p.genre": 2})
	//领劵人数
	totalCouponCountBuilder := squirrel.Select("count(uid)").From(l.svcCtx.SilentStatisticsUser.TableName())
	//用劵人数
	totalUseCouponCountBuilder := squirrel.Select("count(uid)").From(l.svcCtx.SilentStatisticsUser.TableName())
	//pv人数
	TotalPvBuilder := squirrel.Select("count(id)").From(l.svcCtx.SilentStatisticsPv.TableName() + " p").LeftJoin(l.svcCtx.SilentStatisticsUser.TableName() + " u ON p.uid = u.uid").Where(squirrel.Eq{"p.genre": 1})

	if req.PayTimeStart > 0 {
		pageBuilder = pageBuilder.Where(squirrel.And{squirrel.GtOrEq{"o.pay_time": req.PayTimeStart}, squirrel.LtOrEq{"o.pay_time": req.PayTimeEnd}})                                         //分页
		countSubBuilder = countSubBuilder.Where(squirrel.And{squirrel.GtOrEq{"o.pay_time": req.PayTimeStart}, squirrel.LtOrEq{"o.pay_time": req.PayTimeEnd}})                                 //子数据
		totalCsCountBuilder = totalCsCountBuilder.Where(squirrel.And{squirrel.GtOrEq{"add_time": req.PayTimeStart}, squirrel.LtOrEq{"add_time": req.PayTimeEnd}})                             //添加客服
		totalCouponCountBuilder = totalCouponCountBuilder.Where(squirrel.And{squirrel.GtOrEq{"coupon_time": req.PayTimeStart}, squirrel.LtOrEq{"coupon_time": req.PayTimeEnd}})               //领劵
		totalUseCouponCountBuilder = totalUseCouponCountBuilder.Where(squirrel.And{squirrel.GtOrEq{"use_coupon_time": req.PayTimeStart}, squirrel.LtOrEq{"use_coupon_time": req.PayTimeEnd}}) //用劵
		TotalPvBuilder = TotalPvBuilder.Where(squirrel.And{squirrel.GtOrEq{"add_time": req.PayTimeStart}, squirrel.LtOrEq{"add_time": req.PayTimeEnd}})                                       //pv
	} else {
		totalCsCountBuilder = totalCsCountBuilder.Where(squirrel.Gt{"add_time": 0})                      //添加客服
		totalCouponCountBuilder = totalCouponCountBuilder.Where(squirrel.Gt{"coupon_time": 0})           //领劵
		totalUseCouponCountBuilder = totalUseCouponCountBuilder.Where(squirrel.Gt{"use_coupon_time": 0}) //用劵
	}
	if req.UserFilter != "" {
		pageBuilder = pageBuilder.Where(squirrel.Or{squirrel.Like{"u.uid": "%" + req.UserFilter + "%"}, squirrel.Like{"u.name": "%" + req.UserFilter + "%"}})
		countSubBuilder = countSubBuilder.Where(squirrel.Or{squirrel.Like{"u.uid": "%" + req.UserFilter + "%"}, squirrel.Like{"u.name": "%" + req.UserFilter + "%"}})
		totalCsCountBuilder = totalCsCountBuilder.Where(squirrel.Or{squirrel.Like{"u.uid": "%" + req.UserFilter + "%"}, squirrel.Like{"u.name": "%" + req.UserFilter + "%"}})
		totalCouponCountBuilder = totalCouponCountBuilder.Where(squirrel.Or{squirrel.Like{"uid": "%" + req.UserFilter + "%"}, squirrel.Like{"name": "%" + req.UserFilter + "%"}})
		totalUseCouponCountBuilder = totalUseCouponCountBuilder.Where(squirrel.Or{squirrel.Like{"uid": "%" + req.UserFilter + "%"}, squirrel.Like{"name": "%" + req.UserFilter + "%"}})
		TotalPvBuilder = TotalPvBuilder.Where(squirrel.Or{squirrel.Like{"u.uid": "%" + req.UserFilter + "%"}, squirrel.Like{"u.name": "%" + req.UserFilter + "%"}})
	}
	if req.PhoneWeFilter != "" {
		pageBuilder = pageBuilder.Where(squirrel.Or{squirrel.Like{"u.phone": "%" + req.PhoneWeFilter + "%"}, squirrel.Like{"u.we_uid": "%" + req.PhoneWeFilter + "%"}})
		countSubBuilder = countSubBuilder.Where(squirrel.Or{squirrel.Like{"u.phone": "%" + req.PhoneWeFilter + "%"}, squirrel.Like{"u.we_uid": "%" + req.PhoneWeFilter + "%"}})
		totalCsCountBuilder = totalCsCountBuilder.Where(squirrel.Or{squirrel.Like{"u.phone": "%" + req.PhoneWeFilter + "%"}, squirrel.Like{"u.we_uid": "%" + req.PhoneWeFilter + "%"}})
		totalCouponCountBuilder = totalCouponCountBuilder.Where(squirrel.Or{squirrel.Like{"phone": "%" + req.PhoneWeFilter + "%"}, squirrel.Like{"we_uid": "%" + req.PhoneWeFilter + "%"}})
		totalUseCouponCountBuilder = totalUseCouponCountBuilder.Where(squirrel.Or{squirrel.Like{"phone": "%" + req.PhoneWeFilter + "%"}, squirrel.Like{"we_uid": "%" + req.PhoneWeFilter + "%"}})
		TotalPvBuilder = TotalPvBuilder.Where(squirrel.Or{squirrel.Like{"u.phone": "%" + req.PhoneWeFilter + "%"}, squirrel.Like{"u.we_uid": "%" + req.PhoneWeFilter + "%"}})
	}

	switch req.Sort {
	case 1:
		pageBuilder = pageBuilder.OrderBy("order_ct desc")
	case 2:
		pageBuilder = pageBuilder.OrderBy("sum_order_money desc")
	case 3:
		pageBuilder = pageBuilder.OrderBy("last_buy_time desc")
	}

	//分页数据
	wait.Go(func() error {
		return l.svcCtx.SilentStatisticsOrder.FindPageListByPage(l.ctx, pageBuilder, &results, req.Page, req.Limit)
	})

	//统计数据
	countBuilder = countBuilder.FromSelect(countSubBuilder, "purchase_counts")
	countBuilder = countBuilder.Column(squirrel.Expr("(?) as total_cs_count", totalCsCountBuilder))
	countBuilder = countBuilder.Column(squirrel.Expr("(?) as total_coupon_count", totalCouponCountBuilder))
	countBuilder = countBuilder.Column(squirrel.Expr("(?) as total_use_coupon_count", totalUseCouponCountBuilder))
	countBuilder = countBuilder.Column(squirrel.Expr("(?) as pv", TotalPvBuilder))
	wait.Go(func() error {
		return l.svcCtx.SilentStatisticsOrder.FindCustom(l.ctx, countBuilder, &counts)
	})

	err = wait.Wait()
	if err != nil {
		logx.Error("OrderReportLogic wait.Wait err:" + err.Error())
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	resp.Total = counts.TotalUser
	resp.TotalOrderCt = counts.TotalPurchaseCount
	resp.TotalOrderMoney = counts.TotalPurchaseMoney
	resp.TotalAddCs = counts.TotalCsCount
	if counts.Pv > 0 {
		resp.CsRate = counts.TotalCsCount * 100 / counts.Pv
	}
	resp.TotalCoupon = counts.TotalCouponCount
	resp.TotalUseCoupon = counts.TotalUseCouponCount
	resp.Pv = counts.Pv

	for _, r := range results {
		resp.List = append(resp.List, types.SilentStatisticsListInfo{
			Uid:           r.Uid,
			Name:          r.Name,
			Phone:         r.Phone,
			WeUid:         r.WeUid,
			IsAddCs:       r.IsAddCs,
			WakeTime:      common.TimeStampToString(r.WakeTime),
			CouponTime:    common.TimeStampToString(r.CouponTime),
			LastBuyTime:   common.TimeStampToString(r.LastBuyTime),
			OrderCt:       r.OrderCt,
			SumOrderMoney: r.SumOrderMoney,
		})
	}

	return
}
