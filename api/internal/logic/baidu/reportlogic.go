package baidu

import (
	"context"
	"crypto/md5"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"
	"fmt"
	"github.com/Masterminds/squirrel"
	"github.com/spf13/cast"
	"strings"

	"github.com/zeromicro/go-zero/core/logx"
)

type ReportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

type baiduResp struct {
	Code    int64  `json:"error_code"`
	Message string `json:"error_msg"`
}

func NewReportLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReportLogic {
	return &ReportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReportLogic) Report(req *types.BaiduReportReq) error {
	builder := l.svcCtx.ClickCallbackModel.RowBuilder()
	builder = builder.Where(squirrel.Eq{"genre": model.GenreBaidu, "os": req.Os})
	var joinType string
	if req.Os == 0 {
		if req.Oaid != "" {
			builder = builder.Where(squirrel.Eq{"oaid": req.Oaid})
			joinType = "oaid"
		} else if req.Imei != "" {
			joinType = "imei"
			builder = builder.Where(squirrel.Eq{"imei": req.Imei})
		} else {
			//都没有直接返回 匹配不到
			return nil
		}
	} else {
		if req.Idfa == "" {
			//没有直接返回 匹配不到
			return nil
		}
		joinType = "idfa"
		builder = builder.Where(squirrel.Eq{"idfa": req.Idfa})
	}
	result, err := l.svcCtx.ClickCallbackModel.FindOneCustom(l.ctx, builder)
	if err != nil && err != model.ErrNotFound {
		logx.Error(fmt.Sprintf("ReportLogic baidu ClickCallbackModel FindOneCustom err %s", err.Error()))
		return xerr.NewErrCode(xerr.DbError)
	}

	if result != nil {
		//上报
		url := result.Callback
		url = strings.Replace(url, "{{ATYPE}}", req.EventType, 1)
		url = strings.Replace(url, "{{AVALUE}}", cast.ToString(req.EventWeight), 1)
		url += "&join_type=" + joinType
		sign := fmt.Sprintf("%x", md5.New().Sum([]byte(url+result.Akey)))
		url += "&sign=" + sign
		var rsp baiduResp
		post, err := l.svcCtx.RestyHttp.R().SetResult(&rsp).Get(url)
		if err != nil {
			logx.Error(fmt.Sprintf("ReportLogic baidu RestyHttp err %s", err.Error()))
			return xerr.NewErrMsg("baidu report err: " + err.Error())
		}

		logx.Info("ReportLogic baidu RestyHttp info %s", string(post.Body()))
	}

	return nil
}
