package report

import (
	"context"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ReportButtonByRegionListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewReportButtonByRegionListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReportButtonByRegionListLogic {
	return &ReportButtonByRegionListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReportButtonByRegionListLogic) ReportButtonByRegionList(req *types.ReportButtonByRegionListReq) (resp *types.ReportButtonByRegionListResp, err error) {
	where := squirrel.Eq{"region_id": req.RegionsId, "channel": req.ChannelId}
	total, err := l.svcCtx.ReportModel.FindCount(l.ctx, l.svcCtx.ReportModel.CountBuilder("DISTINCT(button_id)").Where(where))
	if err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	resp = new(types.ReportButtonByRegionListResp)
	resp.Total = total

	if total > 0 {
		builder := squirrel.Select("DISTINCT(button_id)").From(l.svcCtx.ReportModel.GetTable()).
			Where(where)
		var result []int64
		if err := l.svcCtx.ReportModel.FindPageListByPage(l.ctx, builder, &result, req.Page, req.Limit); err != nil {
			return nil, xerr.NewErrCode(xerr.DbError)
		}

		if len(result) > 0 {
			for _, buttonId := range result {
				resp.Buttons = append(resp.Buttons, &types.ReportButtonInfo{ButtonId: buttonId})
			}
		}
	}

	if len(resp.Buttons) < 1 {
		resp.Buttons = []interface{}{}
	}

	return resp, nil
}
