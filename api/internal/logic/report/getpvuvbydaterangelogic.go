package report

import (
	"context"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPvUvByDateRangeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetPvUvByDateRangeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPvUvByDateRangeLogic {
	return &GetPvUvByDateRangeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPvUvByDateRangeLogic) GetPvUvByDateRange(req *types.GetPvUvByDateRangeReq) (resp *types.GetPvUvByDateRangeResp, err error) {
	where := squirrel.And{squirrel.Eq{"channel": req.ChannelId}}
	startD := strings.Split(req.StartDate, "-")
	endD := strings.Split(req.EndDate, "-")
	if len(startD) != 3 || len(endD) != 3 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "日期格式有误")
	}
	startT, _ := time.ParseInLocation("2006-01-02", req.StartDate, time.Local)
	startTime := startT.Unix()
	endT, _ := time.ParseInLocation("2006-01-02", req.EndDate, time.Local)
	endTime := endT.Unix() + 86399
	if req.RegionId != 0 {
		where = append(where, squirrel.Eq{"region_id": req.RegionId})
	}
	if req.ButtonId != 0 {
		where = append(where, squirrel.Eq{"button_id": req.ButtonId})
	}

	where = append(where, squirrel.GtOrEq{"created_time": startTime}, squirrel.LtOrEq{"created_time": endTime})

	builder := squirrel.Select("FROM_UNIXTIME(created_time, '%Y-%m-%d') as date,COUNT(*) as pv,COUNT(DISTINCT uid) as uv").
		From(l.svcCtx.ReportModel.GetTable()).Where(where).GroupBy("FROM_UNIXTIME(created_time, '%Y-%m-%d')").OrderBy("date asc")

	var result []*model.VhReportPvUvByDateRange
	err = l.svcCtx.ReportModel.FindRows(l.ctx, builder, &result)
	if err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	resp = new(types.GetPvUvByDateRangeResp)
	resp.List = make([]types.GetPvUvByDateRangeInfo, 0)
	for _, dateRange := range result {
		resp.List = append(resp.List, types.GetPvUvByDateRangeInfo{
			Date: dateRange.Date,
			ReportPUv: types.ReportPUv{
				Pv: dateRange.Pv,
				Uv: dateRange.Uv,
			},
		})
	}

	return
}
