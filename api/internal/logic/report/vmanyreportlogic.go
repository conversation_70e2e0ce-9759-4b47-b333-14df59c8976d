package report

import (
	"context"
	"engine/api/internal/svc"
	"engine/common"
	victoriaMe "engine/common/victoriaMetrics"
	"engine/common/xerr"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
	"net/url"
	"time"
)

type VmAnyReportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewVmAnyReportLogic(ctx context.Context, svcCtx *svc.ServiceContext) *VmAnyReportLogic {
	return &VmAnyReportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *VmAnyReportLogic) VmAnyReport(req map[string]interface{}) error {
	ls := make([]struct {
		ID         int64
		UID        string
		Device     string
		MetricName string
		TimeStamp  int64
	}, 0)

	if t, ok := req["table"]; ok && t == "vh_periods_comment" {
		//评论上报
		if req["audit_status"] == "1" {
			ls = append(ls, struct {
				ID         int64
				UID        string
				Device     string
				MetricName string
				TimeStamp  int64
			}{ID: cast.ToInt64(req["period"]), UID: cast.ToString(req["uid"]), Device: "", MetricName: victoriaMe.PeriodComment, TimeStamp: time.Now().UnixMilli()})
		}
	}

	if t, ok := req["route_url"]; ok && t == "/commodities/v3/package/getPeriodsPackageInventory" {
		//点击量,秒发地址为：/commodities/v3/package/getPeriodsSecondPackageInventory，后期用的时候加上即可
		val, _ := url.ParseQuery(cast.ToString(req["request_param"]))
		//秒发的跳过
		if val.Get("periods_type") == "1" {
			return nil
		}
		requestClient := cast.ToString(req["request_client"])
		if requestClient == "harmonyos" {
			requestClient = "hm"
		} else if requestClient == "vinehoo-pc" {
			requestClient = "pc"
		}
		//不存在的设备跳过
		//临时关闭ios和安卓
		if !common.InSlice(requestClient, []string{"h5", "ios", "android", "hm", "miniapp", "pc"}) {
			return nil
		}
		ls = append(ls, struct {
			ID         int64
			UID        string
			Device     string
			MetricName string
			TimeStamp  int64
		}{ID: cast.ToInt64(val.Get("period")), UID: cast.ToString(req["uid"]), Device: requestClient, MetricName: victoriaMe.PeriodClicks, TimeStamp: time.Now().UnixMilli()})
	}

	if len(ls) > 0 {
		err := l.svcCtx.Metrics.PeriodReportBatchMetrics(ls)
		if err != nil {
			l.Logger.Error("上报报错:" + err.Error())
			return xerr.NewErrMsg("指标数据上报失败")
		}
	}

	return nil
}
