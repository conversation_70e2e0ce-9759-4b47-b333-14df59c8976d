package huawei

import (
	"context"
	"engine/common/model"
	"engine/common/xerr"
	"fmt"
	"github.com/Masterminds/squirrel"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CallbackLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCallbackLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CallbackLogic {
	return &CallbackLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CallbackLogic) Callback(req *types.HuaweiCallbackReq) error {
	builder := model.CountBuilder("*", l.svcCtx.ClickCallbackModel.TableName())
	builder = builder.Where(squirrel.And{squirrel.Eq{"genre": model.GenreHuawei, "os": 0, "oaid": req.Oaid, "imei": req.Imei}})
	count, err := l.svcCtx.ClickCallbackModel.FindCount(l.ctx, builder)
	if err != nil {
		logx.Error(fmt.Sprintf("CallbackLogic huawei ClickCallbackModel FindCount err %s", err.Error()))
		return xerr.NewErrCode(xerr.DbError)
	}

	if count == 0 {
		_, err := l.svcCtx.ClickCallbackModel.Insert(l.ctx, &model.VhClickCallback{
			Os:       0,
			Genre:    model.GenreHuawei,
			Oaid:     req.Oaid,
			Imei:     req.Imei,
			Idfa:     "",
			Akey:     "",
			Callback: req.Callback,
		})
		if err != nil {
			logx.Error(fmt.Sprintf("CallbackLogic huawei ClickCallbackModel Insert err %s", err.Error()))
			return xerr.NewErrCode(xerr.DbError)
		}
	}
	return nil
}
