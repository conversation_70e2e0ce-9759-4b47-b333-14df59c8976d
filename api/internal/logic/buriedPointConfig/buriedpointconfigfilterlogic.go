package buriedPointConfig

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
)

type BuriedPointConfigFilterLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBuriedPointConfigFilterLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BuriedPointConfigFilterLogic {
	return &BuriedPointConfigFilterLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BuriedPointConfigFilterLogic) BuriedPointConfigFilter() (resp *types.BuriedPointConfigFilterResp, err error) {
	result := types.BuriedPointConfigFilterResp{
		Channels: make([]types.BuriedPointConfigFilterRespChannels, 0),
	}
	resp = &result

	// 获取列表
	builder := squirrel.Select("*").From(l.svcCtx.BuriedPointConfigModel.GetTable())
	list := make([]model.VhBuriedPointConfig, 0)
	xerr := l.svcCtx.BuriedPointConfigModel.FindRows(context.Background(), builder, &list)
	if xerr != nil {
		return
	}
	regions := make(map[int64][]types.BuriedPointConfigFilterRespRegion)
	for _, v1 := range list {
		if _, ok := regions[v1.Channel]; !ok {
			regions[v1.Channel] = []types.BuriedPointConfigFilterRespRegion{
				{
					RegionsId:  v1.RegionId,
					RegionName: v1.RegionName,
				},
			}
		} else {
			regions[v1.Channel] = append(regions[v1.Channel], types.BuriedPointConfigFilterRespRegion{
				RegionsId:  v1.RegionId,
				RegionName: v1.RegionName,
			})
		}
	}

	var channels []VinehooButton
	builder = squirrel.Select("id,name as title").From("vh_buried_point_channel")
	l.svcCtx.BuriedPointConfigModel.FindRows(context.Background(), builder, &channels)

	for _, v := range channels {
		s_regions := make([]types.BuriedPointConfigFilterRespRegion, 0)
		if val, ok := regions[v.Id]; ok {
			s_regions = val
		}
		if len(s_regions) == 0 {
			continue
		}
		result.Channels = append(result.Channels, types.BuriedPointConfigFilterRespChannels{
			ChannelId:   v.Id,
			ChannelName: v.Title,
			Regions:     s_regions,
		})
	}

	return
}
