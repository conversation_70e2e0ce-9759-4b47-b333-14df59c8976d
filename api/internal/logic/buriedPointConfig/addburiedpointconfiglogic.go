package buriedPointConfig

import (
	"context"
	"time"

	"engine/common/model"

	"github.com/Masterminds/squirrel"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddBuriedPointConfigLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddBuriedPointConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddBuriedPointConfigLogic {
	return &AddBuriedPointConfigLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddBuriedPointConfigLogic) AddBuriedPointConfig(req *types.AddBuriedPointConfigReq) error {
	builder := squirrel.Select("count(1)").
		From(l.svcCtx.BuriedPointConfigModel.GetTable()).
		Where(squirrel.Eq{"region_id": req.RegionId})
	count, err := l.svcCtx.BuriedPointConfigModel.FindCount(context.Background(), builder)
	if err != nil {
		return xerr.NewErrMsg("添加埋点配置失败1")
	}
	if count > 0 {
		return xerr.NewErrMsg("该区域已存在埋点配置")
	}
	_, err = l.svcCtx.BuriedPointConfigModel.Insert(context.Background(), &model.VhBuriedPointConfig{
		Genre:       req.Genre,
		Channel:     req.Channel,
		RegionId:    req.RegionId,
		RegionName:  req.RegionName,
		CreatedTime: time.Now().Unix(),
	})
	if err != nil {
		return xerr.NewErrMsg(err.Error())
	}

	return nil
}
