package byteDance

import (
	"context"
	"engine/common/model"
	"engine/common/xerr"
	"fmt"
	"github.com/Masterminds/squirrel"
	"github.com/spf13/cast"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ReportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

const (
	url = "https://analytics.oceanengine.com/api/v2/conversion"
)

type byteResp struct {
	Code    int64  `json:"code"`
	Message string `json:"message"`
}

type userResp struct {
	Code    int64  `json:"error_code"`
	Message string `json:"error_msg"`
}

func NewReportLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReportLogic {
	return &ReportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReportLogic) Report(req *types.ByteDanceReportReq) error {
	builder := l.svcCtx.ClickCallbackModel.RowBuilder()
	builder = builder.Where(squirrel.Eq{"genre": model.GenreByte, "os": req.Os})
	var platform string
	if req.Os == 0 {
		platform = "android"
		if req.Oaid != "" {
			builder = builder.Where(squirrel.Eq{"oaid": req.Oaid})
		} else if req.Imei != "" {
			builder = builder.Where(squirrel.Eq{"imei": req.Imei})
		} else {
			//都没有直接返回 匹配不到
			return nil
		}
	} else {
		platform = "ios"
		if req.Idfa == "" {
			//没有直接返回 匹配不到
			return nil
		}
		builder = builder.Where(squirrel.Eq{"idfa": req.Idfa})
	}
	result, err := l.svcCtx.ClickCallbackModel.FindOneCustom(l.ctx, builder)
	if err != nil && err != model.ErrNotFound {
		logx.Error(fmt.Sprintf("ReportLogic byte ClickCallbackModel FindOneCustom err %s", err.Error()))
		return xerr.NewErrCode(xerr.DbError)
	}

	if result != nil {
		//上报
		body := fmt.Sprintf(`
			{
				"event_type": "%s",
				"event_weight": %f,
				"context": {
					"ad": {
						"callback": "%s",
						"match_type": 0
					},
					"device": {
						"platform": "%s",
						"oaid": "%s",
						"imei": "%s",
						"idfa": "%s"
					}
				},
				"timestamp": %d
			}`,
			req.EventType, req.EventWeight, result.Callback, platform, result.Oaid, result.Imei, result.Idfa, time.Now().Unix()*1000)

		var rsp byteResp
		post, err := l.svcCtx.RestyHttp.R().SetResult(&rsp).SetBody(body).Post(url)
		if err != nil {
			logx.Error(fmt.Sprintf("ReportLogic byte RestyHttp err %s", err.Error()))
			return xerr.NewErrMsg("byteDance report err: " + err.Error())
		}

		//更新ios三方注册来源
		if req.Os == 1 && req.EventType == "active_register" {
			uid := cast.ToInt64(l.ctx.Value("uid"))
			if uid > 0 {
				var userRsp userResp
				userBody, err := l.svcCtx.RestyHttp.R().SetResult(&userRsp).SetBody(map[string]interface{}{
					"uid":               uid,
					"tripartite_source": "vinehoo-douyin-IOS",
				}).Post(l.svcCtx.Config.UserHost + "/user/v3/updateUserData")
				if err != nil {
					logx.Error(fmt.Sprintf("ReportLogic user RestyHttp err %s", err.Error()))
				} else {
					logx.Info("ReportLogic user RestyHttp info %s", string(userBody.Body()))
				}
			}
		}

		logx.Info("ReportLogic byte RestyHttp info %s", string(post.Body()))
	}

	return nil
}
