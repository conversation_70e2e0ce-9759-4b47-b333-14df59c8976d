package byteDance

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"
	"fmt"
	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/logx"
)

type CallbackLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCallbackLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CallbackLogic {
	return &CallbackLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CallbackLogic) Callback(req *types.ByteDanceCallbackReq) error {
	builder := model.CountBuilder("*", l.svcCtx.ClickCallbackModel.TableName())
	builder = builder.Where(squirrel.And{squirrel.Eq{"genre": model.GenreByte, "os": req.<PERSON><PERSON>, "oaid": req.<PERSON>, "imei": req.<PERSON><PERSON>, "idfa": req.Idfa}})
	count, err := l.svcCtx.ClickCallbackModel.FindCount(l.ctx, builder)
	if err != nil {
		logx.Error(fmt.Sprintf("CallbackLogic byte ClickCallbackModel FindCount err %s", err.Error()))
		return xerr.NewErrCode(xerr.DbError)
	}

	if count == 0 {
		_, err := l.svcCtx.ClickCallbackModel.Insert(l.ctx, &model.VhClickCallback{
			Os:       req.Os,
			Genre:    model.GenreByte,
			Oaid:     req.Oaid,
			Imei:     req.Imei,
			Idfa:     req.Idfa,
			Akey:     "",
			Callback: req.Callback,
		})
		if err != nil {
			logx.Error(fmt.Sprintf("CallbackLogic byte ClickCallbackModel Insert err %s", err.Error()))
			return xerr.NewErrCode(xerr.DbError)
		}
	}
	return nil
}
