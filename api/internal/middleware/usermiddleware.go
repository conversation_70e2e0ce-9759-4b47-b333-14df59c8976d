package middleware

import (
	"context"
	"engine/common/result"
	"engine/common/xerr"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
)

type UserMiddleware struct {
}

func NewUserMiddleware() *UserMiddleware {
	return &UserMiddleware{}
}

func (m *UserMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		uid := cast.ToInt64(r.Header.Get("vinehoo-uid"))
		if uid == 0 {
			httpx.WriteJson(w, 200, result.Error(xerr.UserNotExist, xerr.MapErrMsg(xerr.UserNotExist)))
			return
		}
		r = r.WithContext(context.WithValue(r.Context(), "uid", uid))
		next(w, r)

		//关闭连接复用 keep-alive
		//w.Header().Set("connection","close")
	}
}
