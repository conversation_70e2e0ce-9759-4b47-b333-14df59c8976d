package task

import (
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

// Task 是一个接口，定义了所有任务需要实现的接口
type Task interface {
	Execute()                   // 定义具体业务逻辑的执行
	Stop()                      // 退出
	GetInterval() time.Duration // 获取任务执行的间隔时间
}

// BaseTask 是一个基础的任务结构体，实现了一些通用方法
type BaseTask struct {
	Interval time.Duration
}

func (t *BaseTask) GetInterval() time.Duration {
	return t.Interval
}

// StartTask 是一个通用的启动任务的方法，通过协程启动
func StartTask(t Task) {
	//启动时执行一次
	defer func() {
		if err := recover(); err != nil {
			logx.Error("任务执行异常:", err)
		}
	}()

	t.Execute()
	ticker := time.NewTicker(t.GetInterval())
	defer ticker.Stop()
	defer t.Stop()

	for {
		select {
		case <-ticker.C:
			t.Execute()
		}
	}
}
