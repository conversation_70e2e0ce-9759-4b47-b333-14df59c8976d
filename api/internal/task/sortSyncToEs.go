package task

import (
	"context"
	"encoding/json"
	"engine/api/internal/svc"
	"engine/common/xredis"
	"fmt"
	"strconv"
	"time"

	red "github.com/gomodule/redigo/redis"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
)

type SortSyncToEs struct {
	BaseTask
	SvcCtx *svc.ServiceContext
}

func (t *SortSyncToEs) Execute() {
	currTime := time.Now()

	/**
	此任务半小时同步一次，同步到后台的es商品列表去
	1:拿到redis的当前排序数据,vm:product:scores，可以参考vmProduct.go（从这里生成的）
	2:遍历数据,更新es的sort字段,注意 只更新periods_type为0的（其他频道的跳出就行了）,排序需要倒过来，列表是从高到低查询的，每100条更新一次，批量去更新,用文档id的方式去更新
	*/

	// 1. 从Redis获取排序数据
	productScores, err := t.getProductScoresFromRedis()
	if err != nil {
		logx.Error("SortSyncToEs 获取Redis排序数据失败:", err)
		return
	}

	if len(productScores) == 0 {
		return
	}

	// 2. 批量更新ES
	err = t.batchUpdateEsSort(productScores)
	if err != nil {
		logx.Error("SortSyncToEs 批量更新ES排序失败:", err)
		return
	}

	logx.Info(fmt.Sprintf("更新ES排序值完成，共处理%d条数据，用时:%.2f秒", len(productScores), time.Since(currTime).Seconds()))
}

func (t *SortSyncToEs) Stop() {
	fmt.Println("SortSyncToEs 任务退出:", time.Now())
}

// ProductScore 商品排序信息
type ProductScore struct {
	ID    int64   `json:"id"`
	Score float64 `json:"score"`
}

// getProductScoresFromRedis 从Redis获取商品排序数据
func (t *SortSyncToEs) getProductScoresFromRedis() ([]ProductScore, error) {
	var productScores []ProductScore

	// 使用ZREVRANGE获取所有数据，按分数从高到低排序
	result, err := xredis.Do(t.SvcCtx.Redis, func(conn red.Conn) (interface{}, error) {
		return conn.Do("ZREVRANGE", "vm:product:scores", 0, -1, "WITHSCORES")
	})

	if err != nil {
		return nil, fmt.Errorf("SortSyncToEs Redis ZREVRANGE失败: %v", err)
	}

	values, ok := result.([]interface{})
	if !ok {
		return nil, fmt.Errorf("SortSyncToEs Redis返回数据格式错误")
	}

	// 解析Redis返回的数据，格式为 [member1, score1, member2, score2, ...]
	for i := 0; i < len(values); i += 2 {
		if i+1 >= len(values) {
			break
		}

		// 获取商品ID
		idStr, err := red.String(values[i], nil)
		if err != nil {
			logx.Error("SortSyncToEs 解析商品ID失败:", err)
			continue
		}
		productID := cast.ToInt64(idStr)

		// 获取分数
		scoreStr, err := red.String(values[i+1], nil)
		if err != nil {
			logx.Error("SortSyncToEs 解析分数失败:", err)
			continue
		}
		score, err := strconv.ParseFloat(scoreStr, 64)
		if err != nil {
			logx.Error("SortSyncToEs 转换分数失败:", err)
			continue
		}

		productScores = append(productScores, ProductScore{
			ID:    productID,
			Score: score,
		})
	}

	return productScores, nil
}

// batchUpdateEsSort 批量更新ES中的排序字段
func (t *SortSyncToEs) batchUpdateEsSort(productScores []ProductScore) error {
	const batchSize = 100
	totalCount := len(productScores)
	processedCount := 0

	// 分批处理
	for i := 0; i < totalCount; i += batchSize {
		end := i + batchSize
		if end > totalCount {
			end = totalCount
		}

		batch := productScores[i:end]
		err := t.updateBatch(batch, i)
		if err != nil {
			logx.Error(fmt.Sprintf("SortSyncToEs 批次%d-%d更新失败: %v", i, end-1, err))
			continue
		}

		processedCount += len(batch)
	}

	return nil
}

// updateBatch 更新一批商品的排序
func (t *SortSyncToEs) updateBatch(batch []ProductScore, startIndex int) error {
	if len(batch) == 0 {
		return nil
	}

	// 首先查询这批商品的periods_type，只更新periods_type为0的商品
	validProducts, err := t.filterValidProducts(batch)
	fmt.Println(len(validProducts), len(batch))
	return nil
	if err != nil {
		return fmt.Errorf("SortSyncToEs 过滤有效商品失败: %v", err)
	}

	if len(validProducts) == 0 {
		logx.Info(fmt.Sprintf("SortSyncToEs 批次%d中没有需要更新的商品", startIndex))
		return nil
	}

	// 创建批量更新请求
	bulkRequest := t.SvcCtx.EsClient.Bulk()

	// 为每个有效商品找到其在原始batch中的位置，用于计算正确的排序值
	for _, product := range validProducts {
		// 找到该商品在原始batch中的索引
		var originalIndex int
		for j, p := range batch {
			if p.ID == product.ID {
				originalIndex = j
				break
			}
		}

		// 计算全局排序值：startIndex + originalIndex + 1
		// +1是因为排序从1开始，而不是从0开始
		sortValue := startIndex + originalIndex + 1

		updateReq := elastic.NewBulkUpdateRequest().
			Index("vinehoo.periods").
			Id(strconv.FormatInt(product.ID, 10)).
			Doc(map[string]interface{}{
				"sort": sortValue,
			})

		bulkRequest = bulkRequest.Add(updateReq)
	}

	// 执行批量更新
	bulkResponse, err := bulkRequest.Do(context.Background())
	if err != nil {
		return fmt.Errorf("ES批量更新失败: %v", err)
	}

	// 检查是否有失败的更新
	if bulkResponse.Errors {
		failedCount := 0
		for _, item := range bulkResponse.Items {
			for _, result := range item {
				if result.Error != nil {
					failedCount++
					logx.Error(fmt.Sprintf("更新商品%s失败: %v", result.Id, result.Error))
				}
			}
		}
	}

	logx.Info(fmt.Sprintf("批次更新完成，成功更新%d条商品", len(validProducts)))
	return nil
}

// filterValidProducts 过滤出periods_type为0的商品
func (t *SortSyncToEs) filterValidProducts(batch []ProductScore) ([]ProductScore, error) {
	if len(batch) == 0 {
		return nil, nil
	}

	// 构建商品ID列表
	ids := make([]string, len(batch))
	for i, product := range batch {
		ids[i] = strconv.FormatInt(product.ID, 10)
	}

	// 查询ES获取这些商品的periods_type
	searchResult, err := t.SvcCtx.EsClient.Search().
		Index("vinehoo.periods").
		Query(elastic.NewIdsQuery().Ids(ids...)).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include("periods_type")).
		Size(len(ids)).
		Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("ES查询失败: %v", err)
	}

	// 创建ID到periods_type的映射
	idToPeriodsType := make(map[int64]int)
	for _, hit := range searchResult.Hits.Hits {
		var data struct {
			PeriodsType int `json:"periods_type"`
		}
		if err := json.Unmarshal(hit.Source, &data); err == nil {
			id := cast.ToInt64(hit.Id)
			idToPeriodsType[id] = data.PeriodsType
		}
	}

	// 过滤出periods_type为0的商品
	var validProducts []ProductScore
	for _, product := range batch {
		if periodsType, exists := idToPeriodsType[product.ID]; exists && periodsType == 0 {
			validProducts = append(validProducts, product)
		}
	}

	return validProducts, nil
}
