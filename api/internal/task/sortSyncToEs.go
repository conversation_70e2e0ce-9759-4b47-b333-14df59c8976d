package task

import (
	"engine/api/internal/svc"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type SortSyncToEs struct {
	BaseTask
	SvcCtx *svc.ServiceContext
}

func (t *SortSyncToEs) Execute() {
	currTime := time.Now()

	/**
	此任务半小时同步一次，同步到后台的es商品列表去
	1:拿到redis的当前排序数据,vm:product:scores，可以参考vmProduct.go（从这里生成的）
	2:遍历数据,更新es的sort字段,注意 只更新periods_type为0的（其他频道的跳出就行了）,排序需要倒过来，列表是从高到低查询的，每100条更新一次，批量去更新,用文档id的方式去更新
	*/

	logx.Info(fmt.Sprintf("更新ES排序值完成，用时:%.2f ", time.Since(currTime).Seconds()))
}

func (t *SortSyncToEs) Stop() {
	fmt.Println("SortSyncToEs 任务退出:", time.Now())
}
