package task

import (
	"context"
	"encoding/json"
	"engine/api/internal/svc"
	"engine/common"
	"engine/common/xredis"
	"errors"
	"fmt"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
	"golang.org/x/sync/errgroup"

	red "github.com/gomodule/redigo/redis"
	"github.com/olivere/elastic/v7"
)

type RankedProduct struct {
	BaseTask
	SvcCtx *svc.ServiceContext
}

type ProductRanker struct {
	ID           int64     `json:"id"`
	Score        float64   `json:"score"`         //最终得分
	BaseScore    float64   `json:"base_score"`    //基础分（原分值，普品在插入前的分值）
	Click        float64   `json:"click"`         //浏览量
	ClickScore   float64   `json:"click_score"`   //浏览量得分
	Order        float64   `json:"order"`         //订单量
	OrderScore   float64   `json:"order_score"`   //订单量得分
	Comment      float64   `json:"comment"`       //评论量
	CommentScore float64   `json:"comment_score"` //评论量得分
	IsNew        bool      `json:"is_new"`        //是否新品
	DecayFactor  float64   `json:"decay_factor"`  //时间衰减系数
	DecayScore   float64   `json:"decay_score"`   //衰减得分（新品基础分*时间衰减系数）
	Manual       int       `json:"manual"`        //手动排序位置
	ManualScore  float64   `json:"manual_score"`  //手动排序得分
	SalesTime    time.Time `json:"-"`             //上架时间
	LabelIds     []int     `json:"-"`             //商品标签id
	DataJson     string    `json:"-"`             //es商品数据
}

type Cf struct {
	NewWeight        CfInfo            `json:"new_weight"`
	NewClickWeight   CfInfo            `json:"new_click_weight"`
	NewOrderWeight   CfInfo            `json:"new_order_weight"`
	NewCommentWeight CfInfo            `json:"new_comment_weight"`
	InsertInterval   CfInfo            `json:"insert_interval"`
	ClickWeight      CfInfo            `json:"click_weight"`
	OrderWeight      CfInfo            `json:"order_weight"`
	CommentWeight    CfInfo            `json:"comment_weight"`
	TimeWeight       []TimeRangeConfig `json:"time_weight"`
	LabelWeight      []LabelConfig     `json:"label_weight"`
}

func (c Cf) GetLabel(labelId int) (LabelConfig, error) {
	for _, config := range c.LabelWeight {
		if config.LabelId == labelId {
			return config, nil
		}
	}

	return LabelConfig{}, errors.New("label not found")
}

type CfInfo struct {
	Label string  `json:"label"`
	Value float64 `json:"value"`
}

// TimeRangeConfig 时间范围配置
type TimeRangeConfig struct {
	StartHour float64 `json:"start_hour"` // 起始小时
	EndHour   float64 `json:"end_hour"`   // 结束小时
	StartVal  float64 `json:"start_val"`  // 起始系数
	EndVal    float64 `json:"end_val"`    // 结束系数
}

// LabelConfig 标签配置
type LabelConfig struct {
	LabelId    int     `json:"label_id"`    // 标签id
	Label      string  `json:"label"`       // 标签名称
	StartIndex float64 `json:"start_index"` // 开始位置
	EndIndex   float64 `json:"end_index"`   // 结束位置
	Interval   float64 `json:"interval"`    // 间隔
}

type ManualSort struct {
	Pid  int64 `json:"pid"`
	Type int   `json:"type"`
	Val  int   `json:"val"`
}

func (t *RankedProduct) Execute() {
	currTime := time.Now()

	var (
		wait                    errgroup.Group
		products                map[int64]*ProductRanker
		crossIds, secondIds     []int64
		crossSecondDataJsonById map[int64]string
		metrics                 map[int64]map[string]float64
		manualSorting           map[int64]ManualSort //商品id -> 分值
		cf                      Cf
		newIds                  []int64 //新品商品id
		pIds                    []int64 //普品商品id
	)

	// 1. 获取商品基本信息和新品（已包含排序和新品标记）
	wait.Go(func() error {
		products = t.getProductBaseInfo()
		return nil
	})

	// 获取跨境和秒发的所有期数
	wait.Go(func() error {
		crossIds, secondIds, crossSecondDataJsonById = t.geCrossSecondProduct()
		return nil
	})

	// 2. 获取指标数据
	wait.Go(func() error {
		metrics = t.queryMetrics()
		return nil
	})

	//3. 获取配置参数
	wait.Go(func() error {
		b, err := red.Bytes(xredis.Do(t.SvcCtx.Redis, func(conn red.Conn) (interface{}, error) {
			return conn.Do("GET", "vm:cf")
		}))
		if err != nil && !errors.Is(err, red.ErrNil) {
			return err
		}

		if len(b) > 0 {
			err = json.Unmarshal(b, &cf)
			if err != nil {
				return err
			}
		}
		return nil
	})

	//4. 获取手动设置排序的商品
	wait.Go(func() error {
		manualSorting = t.ManualSorting()
		return nil
	})

	err := wait.Wait()
	if err != nil || len(products) == 0 {
		return
	}
	for pid, product := range products {
		m := map[string]float64{"clicks": 0, "comments": 0, "order_ct": 0}
		if info, exists := metrics[pid]; exists {
			m = info
		}

		// 浏览量
		product.Click = m["clicks"]

		// 评论
		product.Comment = m["comments"]

		// 订单
		product.Order = m["order_ct"]

		if product.IsNew {
			//浏览量得分
			product.ClickScore = common.TruncationFloat64(m["clicks"] * cf.NewClickWeight.Value)
			//订单量得分
			product.OrderScore = common.TruncationFloat64(m["order_ct"] * cf.NewOrderWeight.Value)
			//评论量得分
			product.CommentScore = common.TruncationFloat64(m["comments"] * cf.NewCommentWeight.Value)
			//时间衰减系数
			product.DecayFactor = t.CalculateDecayFactor(product.SalesTime, cf.TimeWeight)
			//衰减得分
			product.DecayScore = common.TruncationFloat64(cf.NewWeight.Value * product.DecayFactor)
			//基础分
			product.BaseScore = common.TruncationFloat64(product.DecayScore + product.ClickScore + product.OrderScore + product.CommentScore)
		} else {
			//浏览量得分
			product.ClickScore = common.TruncationFloat64(m["clicks"] * cf.ClickWeight.Value)
			//订单量得分
			product.OrderScore = common.TruncationFloat64(m["order_ct"] * cf.OrderWeight.Value)
			//评论量得分
			product.CommentScore = common.TruncationFloat64(m["comments"] * cf.CommentWeight.Value)
			//基础分
			product.BaseScore = common.TruncationFloat64(product.ClickScore + product.OrderScore + product.CommentScore)
		}

		//最终得分
		product.Score = common.TruncationFloat64(product.BaseScore, 1)
	}

	//转换为切片以便排序,列表规则:每cf.InsertInterval.Value个新品后面跟一个普品
	productSlice := make([]*ProductRanker, 0, len(products))
	for _, product := range products {
		productSlice = append(productSlice, product)
	}

	// 按分数降序排序
	sort.Slice(productSlice, func(i, j int) bool {
		return productSlice[i].Score > productSlice[j].Score
	})

	//按照分数讲普品和新品分离开,和标签商品记录下来(每个商品只获取一个有效标签,多个取第一个有效的)
	labelProIds := make(map[int][]int64) //标签id -> 商品ids[]int64
	//拿到所有需要单独计算的标签id
	labelIds := make([]int, 0)
	for _, config := range cf.LabelWeight {
		//x云集的标签不需要处理
		if config.LabelId == 76 {
			continue
		}
		labelIds = append(labelIds, config.LabelId)
		labelProIds[config.LabelId] = make([]int64, 0)
	}

	tmp := make([]*ProductRanker, 0)
	for _, product := range productSlice {
		isLabel := false
		//过滤x云集商品和新品
		if !common.InIntSlice(76, product.LabelIds) && !product.IsNew {
			//每个商品只获取一个有效标签
			for _, labelId := range product.LabelIds {
				if common.InIntSlice(labelId, labelIds) {
					labelProIds[labelId] = append(labelProIds[labelId], product.ID)
					isLabel = true
					break
				}
			}
		}

		//新品和普品分离开,标签商品不用处理
		if product.IsNew && !isLabel {
			tmp = append(tmp, product)
			//新品
			newIds = append(newIds, product.ID)
		} else {
			//普品
			pIds = append(pIds, product.ID)
		}

	}
	productSlice = tmp

	//每cf.InsertInterval.Value个新品后添加一个普品
	tmp = make([]*ProductRanker, 0)
	insertInterval := cast.ToInt(cf.InsertInterval.Value)
	if insertInterval == 0 {
		insertInterval = 1
	}
	nextInsertPos := insertInterval
	for i, ranker := range productSlice {
		if i == nextInsertPos && len(pIds) > 0 {
			id := pIds[0]
			pIds = pIds[1:]
			pro := products[id]
			pro.Score = common.AddFloatWithPrecision(ranker.Score, 0.04)
			tmp = append(tmp, pro)
			nextInsertPos += insertInterval
		}
		tmp = append(tmp, ranker)
	}

	//剩余的普品放到最后
	for _, id := range pIds {
		tmp = append(tmp, products[id])
	}
	productSlice = tmp

	//按照商品标签调整位置
	tmp = make([]*ProductRanker, 0)
	for _, labelId := range labelIds {
		//拿到配置信息
		labelConfig, err := cf.GetLabel(labelId)
		if err != nil {
			continue
		}
		startIndex := labelConfig.StartIndex
		if startIndex < 1 {
			startIndex = 1
		}
		endIndex := labelConfig.EndIndex
		//间隔多少个商品
		interval := labelConfig.Interval
		//下一次插入的位置
		nextInsertIndex := startIndex
		if proIds, exists := labelProIds[labelId]; exists {
			for _, pid := range proIds {
				pro := products[pid]
				if nextInsertIndex >= endIndex {
					break
				}
				//验证是否有这么多商品
				if nextInsertIndex >= float64(len(productSlice)) {
					break
				}
				//拿到插入位置的分值
				insertScore := common.TruncationFloat64(productSlice[cast.ToInt(nextInsertIndex)].Score, 1)
				pro.Score = common.AddFloatWithPrecision(insertScore, 0.03)
				tmp = append(tmp, pro)
				nextInsertIndex += interval
			}
		}
	}

	// 重新排序
	sort.Slice(productSlice, func(i, j int) bool {
		return productSlice[i].Score > productSlice[j].Score
	})

	//每5个后面跟一个跨境商品，，9 个后跟一个秒发
	tmp = make([]*ProductRanker, 0)
	crossIndex := 5
	secondIndex := 9
	ct := 1
	length := len(productSlice)
	for i, ranker := range productSlice {
		if i == crossIndex && len(crossIds) > 0 {
			id := crossIds[0]
			crossIds = crossIds[1:]
			tmp = append(tmp, &ProductRanker{
				ID:       id,
				Score:    common.AddFloatWithPrecision(common.TruncationFloat64(ranker.Score, 1), 0.02),
				DataJson: crossSecondDataJsonById[id],
			})
			crossIndex = ct*9 + 5
		} else if i == secondIndex && len(secondIds) > 0 {
			id := secondIds[0]
			secondIds = secondIds[1:]
			tmp = append(tmp, &ProductRanker{
				ID:       id,
				Score:    common.AddFloatWithPrecision(common.TruncationFloat64(ranker.Score, 1), 0.02),
				DataJson: crossSecondDataJsonById[id],
			})
			secondIndex = ct*9 + 9
			ct++
		}
		//x云集商品不能连续出现，如果下一个是x云集产品，那么就从下一个的后5个位置开始换，如果第五个是x云集就换第4个，依次处理，都是就不管了
		if common.InIntSlice(76, ranker.LabelIds) {
			//x云集商品不能出现在前5个
			if i < 5 && length >= 5 {
				ranker.Score = common.AddFloatWithPrecision(productSlice[4].Score, -0.01)
			}
			if i+1 < length && common.InIntSlice(76, productSlice[i+1].LabelIds) {
				for j := 5; j > 1; j-- {
					if i+j < length && !common.InIntSlice(76, productSlice[i+j].LabelIds) {
						productSlice[i+1].Score, productSlice[i+j].Score = productSlice[i+j].Score, productSlice[i+1].Score
						break
					}
				}
			}
		}
		tmp = append(tmp, ranker)
	}

	productSlice = tmp

	// 重新排序
	sort.Slice(productSlice, func(i, j int) bool {
		return productSlice[i].Score > productSlice[j].Score
	})

	//手动设置排序
	//先按照手动设置的排序值小到大排好
	manualSorts := make([]ManualSort, 0)
	for _, manualSort := range manualSorting {
		manualSorts = append(manualSorts, manualSort)
	}

	sort.Slice(manualSorts, func(i, j int) bool {
		return manualSorts[i].Val < manualSorts[j].Val
	})

	for _, scoreVal := range manualSorts {
		pid := scoreVal.Pid
		if product, exists := products[pid]; exists {
			if scoreVal.Type == 1 {
				//固定位置
				rk := scoreVal.Val
				if rk <= len(productSlice) {
					product.Score = common.AddFloatWithPrecision(productSlice[rk-1].Score, 0.01)
					//如果上一个分数相同，那么上一个分数+0.01
					if rk-2 >= 0 && productSlice[rk-2].Score == product.Score {
						//如果刚好是自己
						if productSlice[rk-2].ID == pid {
							productSlice[rk-1].Score = common.AddFloatWithPrecision(product.Score, 0.01)
						} else {
							productSlice[rk-2].Score = common.AddFloatWithPrecision(productSlice[rk-2].Score, 0.01)
						}
					}
					//如果下一个分数相同，那么下一个分数-0.01
					if rk < len(productSlice) && productSlice[rk].Score == product.Score {
						productSlice[rk].Score = common.AddFloatWithPrecision(productSlice[rk].Score, -0.01)
					}
				}
			} else {
				//直接用给到的分值
				product.Score = common.AddFloatWithPrecision(float64(scoreVal.Val), 0.01)
			}
		}
		//没次调整后都需要 重新排序
		sort.Slice(productSlice, func(i, j int) bool {
			return productSlice[i].Score > productSlice[j].Score
		})
	}

	// 存储到Redis
	_, err = xredis.Do(t.SvcCtx.Redis, func(conn red.Conn) (interface{}, error) {
		if err := conn.Send("MULTI"); err != nil {
			return nil, err
		}

		if err := conn.Send("DEL", "vm:product:scores"); err != nil {
			return nil, err
		}
		if err := conn.Send("DEL", "vm:product:list"); err != nil {
			return nil, err
		}

		// 按照新的顺序存储商品
		for _, product := range productSlice {
			if err := conn.Send("ZADD", "vm:product:scores", product.Score, product.ID); err != nil {
				return nil, err
			}
			if err := conn.Send("ZADD", "vm:product:list", product.Score, product.DataJson); err != nil {
				return nil, err
			}
			if err := t.storeProductToRedis(conn, product); err != nil {
				return nil, err
			}

			//fmt.Printf("id:%s score:%.2f clickRate:%.2f conversionRate:%.2f salesTrend:%.2f comment:%.2f timeDecay:%.2f sort:%.2f \n", product.ID, product.Score, product.ClickRate, product.ConversionRate, product.SalesTrend, product.Comment, product.TimeDecay, product.SortScore)
		}

		if err := conn.Send("EXPIRE", "vm:product:scores", 86400); err != nil {
			return nil, err
		}
		if err := conn.Send("EXPIRE", "vm:product:list", 86400); err != nil {
			return nil, err
		}

		return conn.Do("EXEC")
	})
	if err != nil {
		logx.Error("VmProduct Redis 写入失败:", err)
	}

	logx.Info(fmt.Sprintf("vm排序完成，用时:%.2f ", time.Since(currTime).Seconds()))
}

// CalculateDecayFactor 计算时间衰减系数
func (t *RankedProduct) CalculateDecayFactor(publishTime time.Time, config []TimeRangeConfig) float64 {
	hoursDiff := time.Since(publishTime).Hours()

	// 超过最大范围时返回1.0
	if hoursDiff > config[len(config)-1].EndHour {
		return 1.0
	}

	// 遍历所有时间范围
	for _, r := range config {
		if hoursDiff >= r.StartHour && hoursDiff <= r.EndHour {
			// 线性插值计算衰减系数
			ratio := (hoursDiff - r.StartHour) / (r.EndHour - r.StartHour)
			decay := r.StartVal + (r.EndVal-r.StartVal)*ratio
			//最低为0.1
			if decay < 0.1 {
				decay = 0.1
			}
			return common.TruncationFloat64(decay)
		}
	}

	return 1.0 // 默认无衰减
}

func (t *RankedProduct) storeProductToRedis(conn red.Conn, product *ProductRanker) error {
	key := fmt.Sprintf("vm:product:details:%d", product.ID)
	data, err := json.Marshal(product)
	if err != nil {
		return err
	}
	if err := conn.Send("SET", key, data); err != nil {
		return err
	}
	if err := conn.Send("EXPIRE", key, 86400); err != nil {
		return err
	}
	return nil
}

// 获取商品基本信息（含上架时间）
func (t *RankedProduct) getProductBaseInfo() map[int64]*ProductRanker {
	products := make(map[int64]*ProductRanker)

	query := elastic.NewBoolQuery().Must(
		elastic.NewTermsQuery("onsale_status", 1, 2),
		elastic.NewTermQuery("is_channel", 0),
		elastic.NewTermsQuery("periods_type", 0),
	)

	// 添加排序条件
	result, err := t.SvcCtx.EsClient.Search().
		Index("vinehoo.periods").
		Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include("onsale_time", "label")).
		Sort("onsale_time", false). // 添加这行，false表示倒序
		Size(5000).
		Do(context.Background())

	if err != nil {
		logx.Error("ES查询失败: ", err)
		return products
	}

	currT := time.Now()

	for _, hit := range result.Hits.Hits {
		var data struct {
			OnsaleTime string `json:"onsale_time"`
			Label      string `json:"label"`
		}
		if err := json.Unmarshal(hit.Source, &data); err == nil {
			salesTime, _ := time.ParseInLocation("2006-01-02 15:04:05", data.OnsaleTime, time.Local)
			id := cast.ToInt64(hit.Id)
			var labelIds []int
			if data.Label != "" {
				labelIds = cast.ToIntSlice(strings.Split(data.Label, ","))
			}
			products[id] = &ProductRanker{
				ID:        id,
				SalesTime: salesTime,
				IsNew:     currT.Unix()-salesTime.Unix() <= 432000, //5天
				LabelIds:  labelIds,
				DataJson:  string(hit.Source),
			}
		} else {
			return products
		}
	}
	return products
}

// 获取跨境和秒发的Id
func (t *RankedProduct) geCrossSecondProduct() ([]int64, []int64, map[int64]string) {
	var crossIds, secondIds []int64
	dataJson := make(map[int64]string)

	query := elastic.NewBoolQuery().Must(
		elastic.NewTermsQuery("is_delete", 0),
		elastic.NewTermsQuery("periods_type", 1, 2),
		elastic.NewTermQuery("is_channel", 0),
		elastic.NewBoolQuery().Should(
			elastic.NewTermsQuery("onsale_status", 1),
			elastic.NewTermsQuery("onsale_status", 2),
			elastic.NewTermsQuery("sellout_sold_out", 1),
		),
	)

	// 添加排序条件
	result, err := t.SvcCtx.EsClient.Search().
		Index("vinehoo.periods").
		Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include("periods_type")).
		SortBy(elastic.NewFieldSort("sort").Desc(), elastic.NewFieldSort("onsale_time").Desc()).
		Size(5000).Do(context.Background())

	if err != nil {
		logx.Error("ES查询失败: ", err)
		return crossIds, secondIds, dataJson
	}

	for _, hit := range result.Hits.Hits {
		var data struct {
			PeriodsType int64 `json:"periods_type"`
		}
		if err := json.Unmarshal(hit.Source, &data); err == nil {
			id := cast.ToInt64(hit.Id)
			dataJson[id] = string(hit.Source)
			if data.PeriodsType == 1 {
				secondIds = append(secondIds, id)
			} else {
				crossIds = append(crossIds, id)
			}
		}
	}

	return crossIds, secondIds, dataJson
}

// queryMetrics 并发重构
func (t *RankedProduct) queryMetrics() map[int64]map[string]float64 {
	metrics := make(map[int64]map[string]float64)
	var mu sync.Mutex
	g, _ := errgroup.WithContext(context.Background())

	queries := []struct {
		query string
		field string
	}{
		//最近曝光量
		//{`sum(sum_over_time(period_exposure[1d])) by (id)`, "exposure"},
		// 最近点击量
		{`sum(sum_over_time(period_clicks[5d])) by (id)`, "clicks"},
		// 最近的订单量
		{`sum(sum_over_time(period_order_ct[5d])) by (id)`, "order_ct"},
		// 最近7天的评论数
		{`sum(sum_over_time(period_comment[7d])) by (id)`, "comments"},
	}

	for _, q := range queries {
		q := q
		g.Go(func() error {
			data := t.querySingleMetric(q.query, q.field)
			if data == nil {
				logx.Errorf("查询指标[%s]失败，已跳过", q.field)
				return nil
			}
			mu.Lock()
			for pid, v := range data {
				if _, exists := metrics[pid]; !exists {
					metrics[pid] = map[string]float64{"exposure": 0, "clicks": 0, "order_ct": 0, "comments": 0}
				}
				metrics[pid][q.field] = v
			}
			mu.Unlock()
			return nil
		})
	}

	_ = g.Wait()
	return metrics
}

// 查询单个指标
func (t *RankedProduct) querySingleMetric(query string, metricName string) map[int64]float64 {
	result, err := t.SvcCtx.Metrics.Query(query)
	if err != nil {
		logx.Errorf("[%s]指标查询失败: %v", metricName, err)
		return nil
	}

	data := make(map[int64]float64)
	for _, item := range result.Data.Result {
		pid := item.Metric["id"]
		if pid == "" {
			continue
		}

		if val, err := strconv.ParseFloat(item.Value[1].(string), 64); err == nil {
			data[cast.ToInt64(pid)] = val
		}
	}
	return data
}

// ManualSorting 获取手动排序位置
func (t *RankedProduct) ManualSorting() map[int64]ManualSort {
	var result map[int64]ManualSort
	//resp, err := http.Get("https://test-wine.wineyun.com/commodities/commodities/v3/evSort/getActiveSortIds")
	//resp, err := http.Get("https://callback.vinehoo.com/commodities/commodities/v3/evSort/getActiveSortIds")
	resp, err := http.Get("http://tp6-commodities/commodities/v3/evSort/getActiveSortIds")
	if err != nil {
		logx.Error("获取手动排序商品ID失败:", err)
	} else {
		defer resp.Body.Close()
		var respJson struct {
			ErrorCode int         `json:"error_code"`
			ErrorMsg  string      `json:"error_msg"`
			Data      interface{} `json:"data"`
		}
		if err := json.NewDecoder(resp.Body).Decode(&respJson); err != nil {
			logx.Error("解析手动排序商品ID失败:", err)
		} else if respJson.ErrorCode == 0 {
			if dt, ok := respJson.Data.(map[string]interface{}); ok {
				result = make(map[int64]ManualSort)
				for s, i := range dt {
					result[cast.ToInt64(s)] = ManualSort{
						Pid:  cast.ToInt64(cast.ToInt64(s)),
						Type: cast.ToInt(i.(map[string]interface{})["type"]),
						Val:  cast.ToInt(i.(map[string]interface{})["val"]),
					}
				}
			}
		}
	}
	return result
}

func (t *RankedProduct) Stop() {
	fmt.Println("VmProduct任务退出:", time.Now())
}
