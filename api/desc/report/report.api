syntax = "v1"

info(
    title: "上报"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    ReportDefaultReq {
        Data []*Default `json:"data" validate:"len=1" v:"数据"`
    }
    Default struct {
        Uid string `json:"uid" validate:"required" v:"用户id"`
        isLogin int64 `json:"is_login" validate:"min=0,max=1" v:"是否登陆"`
        Genre int64 `json:"genre" validate:"min=1,max=3" v:"类型"`
        Channel int64 `json:"channel" validate:"min=0,max=10" v:"频道"`
        RegionId int64 `json:"region_id" validate:"required" v:"区域id"`
        ButtonId int64 `json:"button_id" validate:"min=0" v:"按钮id"`
        Client int64 `json:"client" validate:"min=0,max=4" v:"客户端"`
        Mid int64 `json:"mid,optional" v:"商户id"`
        CreatedTime int64 `json:"created_time" validate:"min=1667974216,timeLt" v:"收集时间戳"`
    }

    ReportChannelRegionListResp {
        Channels []interface{} `json:"channels"`
    }
    ReportChannelRegionInfo struct {
        ChannelId int64 `json:"channelId"`
        ChannelName string `json:"channelName"`
        Regions []*ReportRegionInfo `json:"regions"`
    }
    ReportRegionInfo struct {
        RegionsId int64 `json:"regionsId"`
    }

    ReportButtonByRegionListReq {
        ChannelId int64 `form:"channelId" validate:"min=0" v:"频道id"`
        RegionsId int64 `form:"regionsId" validate:"min=1" v:"区域id"`
        Page int64 `form:"page" validate:"min=1" v:"分页"`
        Limit int64 `form:"limit" validate:"min=5" v:"分页数量"`
    }

    ReportButtonByRegionListResp {
        Buttons []interface{} `json:"buttons"`
        Total int64 `json:"total"`
    }
    ReportButtonInfo struct {
        ButtonId int64 `json:"buttonId"`
    }

    ReportSummaryByDayReq {
        Date string `form:"date" validate:"required" v:"日期"`
        ChannelId int64 `form:"channelId,min:0"`
        RegionId int64 `form:"regionId,optional"`
        ButtonId int64 `form:"buttonId,optional"`
    }
    ReportSummaryByDayResp {
        One ReportPUv `json:"0-1"`
        Two ReportPUv `json:"1-2"`
        Three ReportPUv `json:"2-3"`
        Four ReportPUv `json:"3-4"`
        Five ReportPUv `json:"4-5"`
        Six ReportPUv `json:"5-6"`
        Seven ReportPUv `json:"6-7"`
        Eight ReportPUv `json:"7-8"`
        Nine ReportPUv `json:"8-9"`
        Ten ReportPUv `json:"9-10"`
        Eleven ReportPUv `json:"10-11"`
        Twelve ReportPUv `json:"11-12"`
        Thirteen ReportPUv `json:"12-13"`
        Fourteen ReportPUv `json:"13-14"`
        Fifteen ReportPUv `json:"14-15"`
        Sixteen ReportPUv `json:"15-16"`
        Seventeen ReportPUv `json:"16-17"`
        Eighteen ReportPUv `json:"17-18"`
        Nineteen ReportPUv `json:"18-19"`
        Twenty ReportPUv `json:"19-20"`
        TwentyOne ReportPUv `json:"20-21"`
        TwentyTwo ReportPUv `json:"21-22"`
        TwentyThree ReportPUv `json:"22-23"`
        TwentyFour ReportPUv `json:"23-24"`
    }

    GetPvUvByDateRangeReq {
        StartDate string `form:"start_date" validate:"required" v:"开始日期"`
        EndDate string `form:"end_date" validate:"required" v:"结束日期"`
        ChannelId int64 `form:"channelId,min:0"`
        RegionId int64 `form:"regionId,optional"`
        ButtonId int64 `form:"buttonId,optional"`
    }

    GetPvUvByDateRangeResp {
        List []GetPvUvByDateRangeInfo `json:"list"`
    }
    GetPvUvByDateRangeInfo struct {
        Date string `json:"date"`
        ReportPUv
    }

    ReportPUv struct {
        Pv int64 `json:"pv"`
        Uv int64 `json:"uv"`
    }

    WasmReportReq {
        UserAgent string `json:"user_agent,optional"`
    }

    ReportSummaryReq {
        StartDate string `form:"start_date" validate:"required" v:"开始日期"`
        EndDate string `form:"end_date" validate:"required" v:"结束日期"`
        ChannelId int64 `form:"channelId,optional" v:"频道id"`
        RegionId int64 `form:"regionId,optional" v:"区域id"`
        ButtonId string `form:"buttonId,optional" v:"按钮id"`
    }

    ReportSummaryResp {
        Header []string `json:"header"`
        PV []interface{} `json:"PV"`
        UV []interface{} `json:"UV"`
    }
    
    H5ReportReq {
        Url string `json:"url"`
        ErrorInfo string `json:"error_info"`
    }

    VmProductReportReq {
        Data []*VmProduct `json:"data" validate:"gte=1" v:"数据"`
    }
    VmProduct struct {
        MetricName string `json:"metric_name" validate:"required" v:"指标名"`
        Period int64 `json:"period" validate:"required" v:"期数"`
        PeriodType int64 `json:"period_type" validate:"min=0,max=3" v:"频道"`
        Uid string `json:"uid" validate:"required" v:"用户id"`
        Device string `json:"device" validate:"required" v:"设备"`
        CreatedTime int64 `json:"created_time" validate:"min=1667974216,timeLt" v:"收集时间戳"`
    }
    VmAnyReportReq {

    }
)

@server(
    middleware: Global
    group : report
    prefix :/maidian/v3/report
)

service maidian {
    @handler Report //默认上报
    post /report (ReportDefaultReq)

    @handler ReportChannelRegionList //获取频道对应的区域
    get /ReportChannelRegionList returns (ReportChannelRegionListResp)

    @handler ReportButtonByRegionList //拿到区域对应的按钮id
    get /ReportButtonByRegionList (ReportButtonByRegionListReq) returns (ReportButtonByRegionListResp)

    @handler ReportSummaryByDay //根据天查询统计数据
    get /ReportSummaryByDay (ReportSummaryByDayReq) returns (ReportSummaryByDayResp)

    @handler ReportSummaryByDayExcel //根据天导出统计数据
    get /ReportSummaryByDayExcel (ReportSummaryByDayReq) returns (ReportSummaryByDayResp)

    @handler GetPvUvByDateRange //根据日期范围返回每日pvuv
    get /GetPvUvByDateRange (GetPvUvByDateRangeReq) returns (GetPvUvByDateRangeResp)

    @handler WasmReport //默认上报
    post /wasmReport (WasmReportReq)

    @handler ReportSummary //埋点数据汇总
    get /ReportSummary (ReportSummaryReq) returns (ReportSummaryResp)

    @handler ReportSummaryExcel //埋点数据汇总导出
    get /ReportSummaryExcel (ReportSummaryReq) returns (ReportSummaryResp)
    
    @handler H5Report //h5报错信息搜集
    post /h5Report (H5ReportReq)

    @handler VmProductReport //vm上报
    post /vmProductReport (VmProductReportReq)

    @handler VmAnyReport //vm任意类型上报
    post /vmAnyReport (VmAnyReportReq)
}