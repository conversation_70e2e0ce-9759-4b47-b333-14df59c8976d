syntax = "v1"

info(
    title: "沉默用户统计"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    SilentStatisticsOrderReportReq {
        MainOrderNo string `json:"main_order_no" validate:"required" v:"主订单号"`
        Uid int64 `json:"uid" validate:"required" v:"用户id"`
        UseCouponId int64 `json:"use_coupon_id,default=0"`
        PayMoney float64 `json:"pay_money" validate:"required" v:"支付金额"`
        PayTime int64 `json:"pay_time" validate:"required" v:"支付时间"`
    }

    SilentStatisticsReportReq {
        Genre int64 `json:"genre" validate:"min=1,max=3" v:"类型"`//1进入页面,2领劵,3添加客户
        Uid int64 `json:"uid" validate:"required" v:"用户id"`
    }

    SilentStatisticsListReq {
        Page int64 `form:"page" validate:"min=1" v:"分页"`
        Limit int64 `form:"limit" validate:"min=10" v:"分页数量"`
        PayTimeStart int64 `form:"pay_time_start,optional" v:"支付月份开始时间"`
        PayTimeEnd int64 `form:"pay_time_end,optional" v:"支付月份结束时间"`
        UserFilter string `form:"user_filter,optional" v:"用户id/昵称"`
        PhoneWeFilter string `form:"phone_we_filter,optional" v:"手机号/微信号"`
        CouponTimeStart int64 `form:"coupon_time_start,optional" v:"领劵开始时间"`
        CouponTimeEnd int64 `form:"coupon_time_end,optional" v:"领劵结束时间"`
        IsAddCs int64 `form:"is_add_cs,default=2" v:"是否添加客服"`
        Sort int64 `form:"sort,default=3" v:"排序规则"`//1单量 2金额 3最后一次下单时间
    }

    SilentStatisticsListResp {
        Total int64 `json:"total"`                        //总用户
        Pv int64 `json:"pv"`
        TotalOrderCt int64 `json:"total_order_ct"`        //总单量
        TotalOrderMoney float64 `json:"total_order_money"`//总金额
        TotalAddCs int64 `json:"total_add_cs"`            //总添加客户量
        CsRate int64 `json:"cs_rate"`                     //添加客服率
        TotalCoupon int64 `json:"total_coupon"`           //总领劵人数
                                                          //CouponRate int64 `json:"coupon_rate"`             //领劵率
        TotalUseCoupon int64 `json:"total_use_coupon"`    //总使用劵人数
                                                          //UseCouponRate int64 `json:"use_coupon_rate"`      //使用率
        List []SilentStatisticsListInfo `json:"list"`
    }
    SilentStatisticsListInfo struct {
        Uid int64 `json:"uid"`                                         //用户id
        Name string `json:"name"`                                      //昵称
        Phone string `json:"phone"`                                    //手机号
        WeUid string `json:"we_uid"`                                   //微信号
        IsAddCs int64 `json:"is_add_cs"`                               //是否添加客服 1是 0否
        WakeTime string `json:"wake_time"`                             //唤醒时间
        CouponTime string `json:"coupon_time"`                         //领劵时间
        LastBuyTime string `json:"last_buy_time"`                      //最后下单时间
        OrderCt int64 `json:"order_ct"`                                //订单量
        SumOrderMoney float64 `json:"sum_order_money"`                 //订单总金额
    }
)

@server(
    group : silentStatistics
    prefix :/maidian/v3/silentStatistics
)

service maidian {
    @handler OrderReport //订单上报
    post /orderReport (SilentStatisticsOrderReportReq)

    @handler Report //上报(前端)
    post /report (SilentStatisticsReportReq)

    @handler List //统计页面
    get /list (SilentStatisticsListReq) returns (SilentStatisticsListResp)
}