package main

import (
	"engine/api/internal/task"
	cf "engine/common/config"
	_ "engine/common/logger"
	"fmt"

	"engine/api/internal/config"
	"engine/api/internal/handler"
	"engine/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func main() {

	c := config.NewConfig()
	cf.InitApiConfig(c, "go-maidian", "vinehoo.conf", 0)

	server := rest.MustNewServer(c.RestConf)
	defer server.Stop()

	ctx := svc.NewServiceContext(*c)
	handler.RegisterHandlers(server, ctx)

	taskManager := task.NewTaskManager()

	//商品排序算法处理
	if _, err := taskManager.AddTask("0 * * * * *", &task.RankedProduct{
		SvcCtx: ctx,
	}); err != nil {
		panic(err)
	}
	//商品es排序值按照排序算法每半小时同步一次
	if _, err := taskManager.AddTask("0 */30 * * * *", &task.SortSyncToEs{
		SvcCtx: ctx,
	}); err != nil {
		panic(err)
	}

	taskManager.Start()

	fmt.Printf("Starting server at %s:%d...\n", c.Host, c.Port)
	server.Start()
}
