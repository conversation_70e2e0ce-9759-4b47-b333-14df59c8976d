package main

import (
	"fmt"
	"math"
)

func AddFloatWithPrecision(a, b float64, n ...int) float64 {
	digits := 2 // 默认值
	if len(n) > 0 {
		digits = n[0]
	}
	// 计算转换因子
	factor := math.Pow10(digits)

	// 将浮点数转换为整数（乘以因子后四舍五入）
	aInt := int64(math.Round(a * factor))
	bInt := int64(math.Round(b * factor))

	// 整数相加
	sumInt := aInt + bInt

	// 转换回浮点数
	return float64(sumInt) / factor
}

func main() {
	a := float64(860.3)
	fmt.Println(AddFloatWithPrecision(a, 0.03))
	return
	var slc []int
	j := 2
	next := j
	for i := 0; i < 10; i++ {
		if i == next {
			slc = append(slc, i+10)
			next += j
		}
		slc = append(slc, i)
	}
	fmt.Println(slc)

}
