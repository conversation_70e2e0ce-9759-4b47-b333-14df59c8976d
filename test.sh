#!/usr/bin/env bash

URL="http://go-maidian/maidian/v3/auction/tencentMomentsLogs1"
DATA='{"module_name":"auction","request_method":"GET","host":"uh5.vinehoo.com","route_url":"/auction/v3/bid","request_client":"android","request_client_version":9.12,"request_param":"id=1898\u0026bid_price=37.00\u0026is_anonymous=0\u0026nickname=%E7%B4%A2%E6%96%AF%E7%88%B5%E5%A3%AB\u0026avatar_image=https%3A%2F%2Fimages.vinehoo.com%2Fvinehoo%2Fclient%2Fuser%2Favatar%2F299179.png\u0026province_name=%E5%8C%97%E4%BA%AC","user_agent":"Mozilla/5.0 (Linux; Android 11; meizu 17 Pro Build/QKQ1.200127.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/97.0.4692.98 Mobile Safari/537.36","ip":"*************","uid":"299179","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1aWQiOjI5OTE3OSwibG9naW5fdGltZSI6MTY2NTQ0NjgwMH0.kAJPDYvvjKIu0WgybPUXtTeMm6IH41ircBgFalFyFD8","logon":true,"referer":"https://h5.vinehoo.com/packageH/pages/auction-goods-detail/auction-goods-detail?id=1898","resp_body_size":43,"client_sign":"120856a3333a30c5007802c9256b41ea","request_time":"2023-04-06T22:26:27.541+08:00","response_time":"2023-04-06T22:26:27.549+08:00"}'

count=0
start_time=$(date +%s)
current_time=$start_time

while true; do
# 发送POST请求
response=$(curl -s -X POST -H "Content-Type: application/json" -d "$DATA" "$URL")

# 记录请求完成次数
count=$((count + 1))

# 计算当前时间
current_time=$(date +%s)

# 检查是否已经过了一秒
if [[ $((current_time - start_time)) -ge 1 ]]; then
# 输出每秒请求完成次数
echo $((current_time - start_time))
echo "每秒请求完成次数: $count"

# 重置计数器和开始时间
count=0
start_time=$current_time
fi
done
